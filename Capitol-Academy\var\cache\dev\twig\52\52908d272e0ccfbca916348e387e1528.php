<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/edit.html.twig */
class __TwigTemplate_fb9f337deacad498844e5f8dfb04fa11 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 59
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("course_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"";
        // line 60
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 60, $this->source); })()), "isActive", [], "any", false, false, false, 60)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("1") : ("0"));
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"";
        // line 78
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 78, $this->source); })()), "code", [], "any", false, false, false, 78), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., TRAD101, FIN200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code (e.g., TRAD101, FIN200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"";
        // line 102
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 102, $this->source); })()), "title", [], "any", false, false, false, 102), "html", null, true);
        yield "\"
                                               placeholder=\"Enter comprehensive course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a category...</option>
                                            ";
        // line 131
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 131, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 132
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 132), "html", null, true);
            yield "\" ";
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 132, $this->source); })()), "category", [], "any", false, false, false, 132) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 132))) {
                yield "selected";
            }
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 132), "html", null, true);
            yield "</option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 134
        yield "                                        </select>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                required
                                                aria-describedby=\"level_help level_error\"
                                                aria-label=\"Select a course difficulty level\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" ";
        // line 156
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 156, $this->source); })()), "level", [], "any", false, false, false, 156) == "Beginner")) {
            yield "selected";
        }
        yield ">Beginner</option>
                                            <option value=\"Intermediate\" ";
        // line 157
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 157, $this->source); })()), "level", [], "any", false, false, false, 157) == "Intermediate")) {
            yield "selected";
        }
        yield ">Intermediate</option>
                                            <option value=\"Advanced\" ";
        // line 158
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 158, $this->source); })()), "level", [], "any", false, false, false, 158) == "Advanced")) {
            yield "selected";
        }
        yield ">Advanced</option>
                                        </select>
                                        <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"";
        // line 177
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 177, $this->source); })()), "duration", [], "any", false, false, false, 177), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., 120\"
                                               min=\"1\"
                                               max=\"10000\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration in minutes.
                                        </div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"";
        // line 200
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 200, $this->source); })()), "price", [], "any", false, false, false, 200), "html", null, true);
        yield "\"
                                               placeholder=\"0.00\"
                                               step=\"0.01\"
                                               min=\"0\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter comprehensive course description including objectives, target audience, and key topics...\"
                                          required
                                          style=\"min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">";
        // line 225
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 225, $this->source); })()), "description", [], "any", false, false, false, 225), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a comprehensive course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"learning-outcomes-container\">
                                    ";
        // line 239
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 239, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 239) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 239, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 239)) > 0))) {
            // line 240
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 240, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 240));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 241
                yield "                                            <div class=\"input-group mb-2 learning-outcome-item\">
                                                <input type=\"text\"
                                                       class=\"form-control enhanced-field\"
                                                       name=\"learning_outcomes[]\"
                                                       value=\"";
                // line 245
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "\"
                                                       placeholder=\"e.g., Analyze market trends and identify trading opportunities\"
                                                       required
                                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                <div class=\"input-group-append\">
                                                    ";
                // line 250
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 250)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 251
                    yield "                                                        <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    ";
                } else {
                    // line 255
                    yield "                                                        <button type=\"button\" class=\"btn remove-learning-outcome\" style=\"background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                    ";
                }
                // line 259
                yield "                                                </div>
                                            </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 262
            yield "                                    ";
        } else {
            // line 263
            yield "                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   placeholder=\"e.g., Analyze market trends and identify trading opportunities\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        // line 277
        yield "                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Features <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"features-container\">
                                    ";
        // line 291
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 291, $this->source); })()), "features", [], "any", false, false, false, 291) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 291, $this->source); })()), "features", [], "any", false, false, false, 291)) > 0))) {
            // line 292
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 292, $this->source); })()), "features", [], "any", false, false, false, 292));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 293
                yield "                                            <div class=\"input-group mb-2 feature-item\">
                                                <input type=\"text\"
                                                       class=\"form-control enhanced-field\"
                                                       name=\"features[]\"
                                                       value=\"";
                // line 297
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "\"
                                                       placeholder=\"e.g., Live trading sessions, Real-time market analysis, Downloadable resources\"
                                                       required
                                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                <div class=\"input-group-append\">
                                                    ";
                // line 302
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 302)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 303
                    yield "                                                        <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    ";
                } else {
                    // line 307
                    yield "                                                        <button type=\"button\" class=\"btn remove-feature\" style=\"background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                    ";
                }
                // line 311
                yield "                                                </div>
                                            </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 314
            yield "                                    ";
        } else {
            // line 315
            yield "                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   placeholder=\"e.g., Live trading sessions, Real-time market analysis, Downloadable resources\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        // line 329
        yield "                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            </div>

                            <!-- Course Images -->
                            <div class=\"form-group\">
                                <div class=\"row\">
                                    <!-- Thumbnail Image -->
                                    <div class=\"col-md-6\">
                                        <div class=\"mb-3\">
                                            <label for=\"thumbnail_image\" class=\"form-label\">
                                                <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Thumbnail Image <span class=\"text-danger\">*</span>
                                            </label>
                                            <input type=\"file\"
                                                   class=\"form-control enhanced-file-field\"
                                                   id=\"thumbnail_image\"
                                                   name=\"thumbnail_image\"
                                                   accept=\"image/jpeg,image/png,image/jpg\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                            <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" ";
        // line 352
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 352, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 352)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "style=\"display: block;\"";
        } else {
            yield "style=\"display: none;\"";
        }
        yield ">
                                                <div class=\"professional-image-container mx-auto\" style=\"width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                    <img src=\"";
        // line 354
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 354, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 354)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 354, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 354), "html", null, true);
        }
        yield "\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                                </div>
                                                ";
        // line 356
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 356, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 356)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 357
            yield "                                                    <small class=\"text-muted d-block mt-2\">Current thumbnail</small>
                                                ";
        }
        // line 359
        yield "                                            </div>
                                        </div>
                                    </div>

                                    <!-- Banner Image -->
                                    <div class=\"col-md-6\">
                                        <div class=\"mb-3\">
                                            <label for=\"banner_image\" class=\"form-label\">
                                                <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Banner Image <span class=\"text-danger\">*</span>
                                            </label>
                                            <input type=\"file\"
                                                   class=\"form-control enhanced-file-field\"
                                                   id=\"banner_image\"
                                                   name=\"banner_image\"
                                                   accept=\"image/jpeg,image/png,image/jpg\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                            <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\" ";
        // line 376
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 376, $this->source); })()), "bannerImage", [], "any", false, false, false, 376)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "style=\"display: block;\"";
        } else {
            yield "style=\"display: none;\"";
        }
        yield ">
                                                <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                    <img src=\"";
        // line 378
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 378, $this->source); })()), "bannerImage", [], "any", false, false, false, 378)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 378, $this->source); })()), "bannerUrl", [], "any", false, false, false, 378), "html", null, true);
        }
        yield "\" alt=\"Banner Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                                </div>
                                                ";
        // line 380
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 380, $this->source); })()), "bannerImage", [], "any", false, false, false, 380)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 381
            yield "                                                    <small class=\"text-muted d-block mt-2\">Current banner</small>
                                                ";
        }
        // line 383
        yield "                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           ";
        // line 397
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 397, $this->source); })()), "hasModules", [], "any", false, false, false, 397)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "checked";
        }
        // line 398
        yield "                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: ";
        // line 407
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 407, $this->source); })()), "hasModules", [], "any", false, false, false, 407)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "block";
        } else {
            yield "none";
        }
        yield ";\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    ";
        // line 414
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 414, $this->source); })()), "modules", [], "any", false, false, false, 414) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 414, $this->source); })()), "modules", [], "any", false, false, false, 414)) > 0))) {
            // line 415
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 415, $this->source); })()), "modules", [], "any", false, false, false, 415));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 416
                yield "                                            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                        Module <span class=\"module-number\">";
                // line 420
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 420), "html", null, true);
                yield "</span>
                                                    </h6>
                                                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>

                                                <input type=\"hidden\" name=\"modules[";
                // line 427
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 427), "html", null, true);
                yield "][id]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "id", [], "any", false, false, false, 427), "html", null, true);
                yield "\">

                                                <!-- Module Basic Info -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Code
                                                            </label>
                                                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[";
                // line 437
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 437), "html", null, true);
                yield "][code]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "code", [], "any", false, false, false, 437), "html", null, true);
                yield "\" placeholder=\"e.g., Module-";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 437), "html", null, true);
                yield "\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Title <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[";
                // line 446
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 446), "html", null, true);
                yield "][title]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 446), "html", null, true);
                yield "\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Description -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Description <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[";
                // line 457
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 457), "html", null, true);
                yield "][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 457), "html", null, true);
                yield "</textarea>
                                                </div>

                                                <!-- Module Duration and Price -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Duration (Minutes) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[";
                // line 468
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 468), "html", null, true);
                yield "][duration]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "duration", [], "any", false, false, false, 468), "html", null, true);
                yield "\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required>
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Price (USD) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[";
                // line 477
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 477), "html", null, true);
                yield "][price]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "price", [], "any", false, false, false, 477), "html", null, true);
                yield "\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Learning Outcomes -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Learning Outcomes <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"learning-outcomes-container\">
                                                        ";
                // line 489
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 489) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 489)) > 0))) {
                    // line 490
                    yield "                                                            ";
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 490));
                    $context['loop'] = [
                      'parent' => $context['_parent'],
                      'index0' => 0,
                      'index'  => 1,
                      'first'  => true,
                    ];
                    if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                        $length = count($context['_seq']);
                        $context['loop']['revindex0'] = $length - 1;
                        $context['loop']['revindex'] = $length;
                        $context['loop']['length'] = $length;
                        $context['loop']['last'] = 1 === $length;
                    }
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 491
                        yield "                                                                <div class=\"input-group mb-2\">
                                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[";
                        // line 492
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "parent", [], "any", false, false, false, 492), "loop", [], "any", false, false, false, 492), "index0", [], "any", false, false, false, 492), "html", null, true);
                        yield "][learning_outcomes][]\" value=\"";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                    <div class=\"input-group-append\">
                                                                        ";
                        // line 494
                        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 494)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                            // line 495
                            yield "                                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-plus\"></i>
                                                                            </button>
                                                                        ";
                        } else {
                            // line 499
                            yield "                                                                            <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-minus\"></i>
                                                                            </button>
                                                                        ";
                        }
                        // line 503
                        yield "                                                                    </div>
                                                                </div>
                                                            ";
                        ++$context['loop']['index0'];
                        ++$context['loop']['index'];
                        $context['loop']['first'] = false;
                        if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                            --$context['loop']['revindex0'];
                            --$context['loop']['revindex'];
                            $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                        }
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 506
                    yield "                                                        ";
                } else {
                    // line 507
                    yield "                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[";
                    // line 508
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 508), "html", null, true);
                    yield "][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        ";
                }
                // line 516
                yield "                                                    </div>
                                                </div>

                                                <!-- Module Features -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Features <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"features-container\">
                                                        ";
                // line 526
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 526) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 526)) > 0))) {
                    // line 527
                    yield "                                                            ";
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 527));
                    $context['loop'] = [
                      'parent' => $context['_parent'],
                      'index0' => 0,
                      'index'  => 1,
                      'first'  => true,
                    ];
                    if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                        $length = count($context['_seq']);
                        $context['loop']['revindex0'] = $length - 1;
                        $context['loop']['revindex'] = $length;
                        $context['loop']['length'] = $length;
                        $context['loop']['last'] = 1 === $length;
                    }
                    foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                        // line 528
                        yield "                                                                <div class=\"input-group mb-2\">
                                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[";
                        // line 529
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "parent", [], "any", false, false, false, 529), "loop", [], "any", false, false, false, 529), "index0", [], "any", false, false, false, 529), "html", null, true);
                        yield "][features][]\" value=\"";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                        yield "\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                    <div class=\"input-group-append\">
                                                                        ";
                        // line 531
                        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 531)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                            // line 532
                            yield "                                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-plus\"></i>
                                                                            </button>
                                                                        ";
                        } else {
                            // line 536
                            yield "                                                                            <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-minus\"></i>
                                                                            </button>
                                                                        ";
                        }
                        // line 540
                        yield "                                                                    </div>
                                                                </div>
                                                            ";
                        ++$context['loop']['index0'];
                        ++$context['loop']['index'];
                        $context['loop']['first'] = false;
                        if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                            --$context['loop']['revindex0'];
                            --$context['loop']['revindex'];
                            $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                        }
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 543
                    yield "                                                        ";
                } else {
                    // line 544
                    yield "                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[";
                    // line 545
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 545), "html", null, true);
                    yield "][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        ";
                }
                // line 553
                yield "                                                    </div>
                                                </div>

                                                <!-- Hidden fields -->
                                                <input type=\"hidden\" name=\"modules[";
                // line 557
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 557), "html", null, true);
                yield "][is_active]\" value=\"1\">
                                                <input type=\"hidden\" name=\"modules[";
                // line 558
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 558), "html", null, true);
                yield "][sort_order]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 558), "html", null, true);
                yield "\">
                                            </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 561
            yield "                                    ";
        } else {
            // line 562
            yield "                                        <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                            <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                    <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                    Module <span class=\"module-number\">1</span>
                                                </h6>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>

                                            <!-- Module Basic Info -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Code
                                                        </label>
                                                        <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Title <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Module Description <span class=\"text-danger\">*</span>
                                                </label>
                                                <textarea class=\"form-control enhanced-field module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\"></textarea>
                                            </div>

                                            <!-- Module Duration and Price -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[0][duration]\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required>
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Price (USD) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[0][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Learning Outcomes -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"learning-outcomes-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Features -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Features <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"features-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden fields -->
                                            <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                            <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                        </div>
                                    ";
        }
        // line 667
        yield "                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                                    <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Update Course
                                        </button>
                                        <a href=\"";
        // line 683
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                                    </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Remove Module
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-info-circle text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"mt-3 mb-2\">Module Required</h6>
                <p class=\"text-muted mb-3\"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-primary\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-check me-2\"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class=\"modal fade\" id=\"moduleValidationModal\" tabindex=\"-1\" aria-labelledby=\"moduleValidationModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"moduleValidationModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-exclamation-triangle me-2\"></i>Module Required
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        At least one module is required.
                    </p>
                    <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 749
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 750
        yield "<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    \$(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion (disabled for edit mode)
    // Price formatting
    \$('#price').on('blur', function() {
        var value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    \$('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });

    \$('#banner_image').on('change', function() {
        previewImage(this, '#banner-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = ";
        // line 919
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 919, $this->source); })()), "modules", [], "any", false, false, false, 919)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 919, $this->source); })()), "modules", [], "any", false, false, false, 919)), "html", null, true)) : (0));
        yield ";

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Code
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\">
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Title <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required>
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Module Description <span class=\"text-danger\">*</span>
                    </label>
                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px;\"></textarea>
                </div>

                <!-- Module Duration and Price -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Duration (Minutes) <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[\${moduleIndex}][duration]\" placeholder=\"e.g., 90\" min=\"1\" max=\"1000\" required>
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Price (USD) <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[\${moduleIndex}][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                        </div>
                    </div>
                </div>

                <!-- Module Learning Outcomes -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Learning Outcomes <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"learning-outcomes-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Features <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"features-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
            </div>
        `;
        \$('#modules-container').append(newModule);
        moduleIndex++;
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        // Renumber modules
        \$('#modules-container .module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
        });
        toggleModuleValidation();
    });

    // Enhanced field styling
    \$('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1496 => 919,  1325 => 750,  1312 => 749,  1236 => 683,  1218 => 667,  1111 => 562,  1108 => 561,  1089 => 558,  1085 => 557,  1079 => 553,  1068 => 545,  1065 => 544,  1062 => 543,  1046 => 540,  1040 => 536,  1034 => 532,  1032 => 531,  1025 => 529,  1022 => 528,  1004 => 527,  1002 => 526,  990 => 516,  979 => 508,  976 => 507,  973 => 506,  957 => 503,  951 => 499,  945 => 495,  943 => 494,  936 => 492,  933 => 491,  915 => 490,  913 => 489,  896 => 477,  882 => 468,  866 => 457,  850 => 446,  834 => 437,  819 => 427,  809 => 420,  803 => 416,  785 => 415,  783 => 414,  769 => 407,  758 => 398,  754 => 397,  738 => 383,  734 => 381,  732 => 380,  725 => 378,  716 => 376,  697 => 359,  693 => 357,  691 => 356,  684 => 354,  675 => 352,  650 => 329,  634 => 315,  631 => 314,  615 => 311,  609 => 307,  603 => 303,  601 => 302,  593 => 297,  587 => 293,  569 => 292,  567 => 291,  551 => 277,  535 => 263,  532 => 262,  516 => 259,  510 => 255,  504 => 251,  502 => 250,  494 => 245,  488 => 241,  470 => 240,  468 => 239,  451 => 225,  423 => 200,  397 => 177,  373 => 158,  367 => 157,  361 => 156,  337 => 134,  322 => 132,  318 => 131,  286 => 102,  259 => 78,  238 => 60,  234 => 59,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_courses') }}\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"{{ path('admin_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('course_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"{{ course.isActive ? '1' : '0' }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"{{ course.code }}\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., TRAD101, FIN200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code (e.g., TRAD101, FIN200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"{{ course.title }}\"
                                               placeholder=\"Enter comprehensive course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value=\"{{ category.name }}\" {% if course.category == category.name %}selected{% endif %}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\" aria-hidden=\"true\"></i>
                                            Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                required
                                                aria-describedby=\"level_help level_error\"
                                                aria-label=\"Select a course difficulty level\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" {% if course.level == 'Beginner' %}selected{% endif %}>Beginner</option>
                                            <option value=\"Intermediate\" {% if course.level == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                            <option value=\"Advanced\" {% if course.level == 'Advanced' %}selected{% endif %}>Advanced</option>
                                        </select>
                                        <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"{{ course.duration }}\"
                                               placeholder=\"e.g., 120\"
                                               min=\"1\"
                                               max=\"10000\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration in minutes.
                                        </div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class=\"col-md-3\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"{{ course.price }}\"
                                               placeholder=\"0.00\"
                                               step=\"0.01\"
                                               min=\"0\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter comprehensive course description including objectives, target audience, and key topics...\"
                                          required
                                          style=\"min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">{{ course.description }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a comprehensive course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"learning-outcomes-container\">
                                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                                        {% for outcome in course.learningOutcomes %}
                                            <div class=\"input-group mb-2 learning-outcome-item\">
                                                <input type=\"text\"
                                                       class=\"form-control enhanced-field\"
                                                       name=\"learning_outcomes[]\"
                                                       value=\"{{ outcome }}\"
                                                       placeholder=\"e.g., Analyze market trends and identify trading opportunities\"
                                                       required
                                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                <div class=\"input-group-append\">
                                                    {% if loop.first %}
                                                        <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type=\"button\" class=\"btn remove-learning-outcome\" style=\"background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   placeholder=\"e.g., Analyze market trends and identify trading opportunities\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Features <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"features-container\">
                                    {% if course.features and course.features|length > 0 %}
                                        {% for feature in course.features %}
                                            <div class=\"input-group mb-2 feature-item\">
                                                <input type=\"text\"
                                                       class=\"form-control enhanced-field\"
                                                       name=\"features[]\"
                                                       value=\"{{ feature }}\"
                                                       placeholder=\"e.g., Live trading sessions, Real-time market analysis, Downloadable resources\"
                                                       required
                                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                <div class=\"input-group-append\">
                                                    {% if loop.first %}
                                                        <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type=\"button\" class=\"btn remove-feature\" style=\"background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   placeholder=\"e.g., Live trading sessions, Real-time market analysis, Downloadable resources\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            </div>

                            <!-- Course Images -->
                            <div class=\"form-group\">
                                <div class=\"row\">
                                    <!-- Thumbnail Image -->
                                    <div class=\"col-md-6\">
                                        <div class=\"mb-3\">
                                            <label for=\"thumbnail_image\" class=\"form-label\">
                                                <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Thumbnail Image <span class=\"text-danger\">*</span>
                                            </label>
                                            <input type=\"file\"
                                                   class=\"form-control enhanced-file-field\"
                                                   id=\"thumbnail_image\"
                                                   name=\"thumbnail_image\"
                                                   accept=\"image/jpeg,image/png,image/jpg\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                            <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" {% if course.thumbnailImage %}style=\"display: block;\"{% else %}style=\"display: none;\"{% endif %}>
                                                <div class=\"professional-image-container mx-auto\" style=\"width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                    <img src=\"{% if course.thumbnailImage %}{{ course.thumbnailUrl }}{% endif %}\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                                </div>
                                                {% if course.thumbnailImage %}
                                                    <small class=\"text-muted d-block mt-2\">Current thumbnail</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Banner Image -->
                                    <div class=\"col-md-6\">
                                        <div class=\"mb-3\">
                                            <label for=\"banner_image\" class=\"form-label\">
                                                <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Banner Image <span class=\"text-danger\">*</span>
                                            </label>
                                            <input type=\"file\"
                                                   class=\"form-control enhanced-file-field\"
                                                   id=\"banner_image\"
                                                   name=\"banner_image\"
                                                   accept=\"image/jpeg,image/png,image/jpg\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                            <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\" {% if course.bannerImage %}style=\"display: block;\"{% else %}style=\"display: none;\"{% endif %}>
                                                <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                    <img src=\"{% if course.bannerImage %}{{ course.bannerUrl }}{% endif %}\" alt=\"Banner Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                                </div>
                                                {% if course.bannerImage %}
                                                    <small class=\"text-muted d-block mt-2\">Current banner</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           {% if course.hasModules %}checked{% endif %}
                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: {% if course.hasModules %}block{% else %}none{% endif %};\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    {% if course.modules and course.modules|length > 0 %}
                                        {% for module in course.modules %}
                                            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                        Module <span class=\"module-number\">{{ loop.index }}</span>
                                                    </h6>
                                                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>

                                                <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][id]\" value=\"{{ module.id }}\">

                                                <!-- Module Basic Info -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Code
                                                            </label>
                                                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[{{ loop.index0 }}][code]\" value=\"{{ module.code }}\" placeholder=\"e.g., Module-{{ loop.index }}\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Title <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[{{ loop.index0 }}][title]\" value=\"{{ module.title }}\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Description -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Description <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[{{ loop.index0 }}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\">{{ module.description }}</textarea>
                                                </div>

                                                <!-- Module Duration and Price -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Duration (Minutes) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[{{ loop.index0 }}][duration]\" value=\"{{ module.duration }}\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required>
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Price (USD) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[{{ loop.index0 }}][price]\" value=\"{{ module.price }}\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Learning Outcomes -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Learning Outcomes <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"learning-outcomes-container\">
                                                        {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                            {% for outcome in module.learningOutcomes %}
                                                                <div class=\"input-group mb-2\">
                                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[{{ loop.parent.loop.index0 }}][learning_outcomes][]\" value=\"{{ outcome }}\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                    <div class=\"input-group-append\">
                                                                        {% if loop.first %}
                                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-plus\"></i>
                                                                            </button>
                                                                        {% else %}
                                                                            <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-minus\"></i>
                                                                            </button>
                                                                        {% endif %}
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                        {% else %}
                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[{{ loop.index0 }}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Module Features -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Features <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"features-container\">
                                                        {% if module.features and module.features|length > 0 %}
                                                            {% for feature in module.features %}
                                                                <div class=\"input-group mb-2\">
                                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[{{ loop.parent.loop.index0 }}][features][]\" value=\"{{ feature }}\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                    <div class=\"input-group-append\">
                                                                        {% if loop.first %}
                                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-plus\"></i>
                                                                            </button>
                                                                        {% else %}
                                                                            <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                                <i class=\"fas fa-minus\"></i>
                                                                            </button>
                                                                        {% endif %}
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                        {% else %}
                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[{{ loop.index0 }}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Hidden fields -->
                                                <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][is_active]\" value=\"1\">
                                                <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][sort_order]\" value=\"{{ loop.index }}\">
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                            <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                    <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                    Module <span class=\"module-number\">1</span>
                                                </h6>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>

                                            <!-- Module Basic Info -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Code
                                                        </label>
                                                        <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Title <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Module Description <span class=\"text-danger\">*</span>
                                                </label>
                                                <textarea class=\"form-control enhanced-field module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\"></textarea>
                                            </div>

                                            <!-- Module Duration and Price -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[0][duration]\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required>
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Price (USD) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[0][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Learning Outcomes -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"learning-outcomes-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Features -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Features <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"features-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden fields -->
                                            <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                            <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                        </div>
                                    {% endif %}
                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                                    <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Update Course
                                        </button>
                                        <a href=\"{{ path('admin_courses') }}\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                                    </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Remove Module
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-info-circle text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"mt-3 mb-2\">Module Required</h6>
                <p class=\"text-muted mb-3\"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-primary\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-check me-2\"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class=\"modal fade\" id=\"moduleValidationModal\" tabindex=\"-1\" aria-labelledby=\"moduleValidationModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"moduleValidationModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-exclamation-triangle me-2\"></i>Module Required
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        At least one module is required.
                    </p>
                    <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    \$(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion (disabled for edit mode)
    // Price formatting
    \$('#price').on('blur', function() {
        var value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    \$('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });

    \$('#banner_image').on('change', function() {
        previewImage(this, '#banner-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = {{ course.modules ? course.modules|length : 0 }};

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Code
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\">
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Title <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required>
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Module Description <span class=\"text-danger\">*</span>
                    </label>
                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px;\"></textarea>
                </div>

                <!-- Module Duration and Price -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Duration (Minutes) <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"number\" class=\"form-control enhanced-field module-duration\" name=\"modules[\${moduleIndex}][duration]\" placeholder=\"e.g., 90\" min=\"1\" max=\"1000\" required>
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Price (USD) <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"number\" class=\"form-control enhanced-field module-price\" name=\"modules[\${moduleIndex}][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required>
                        </div>
                    </div>
                </div>

                <!-- Module Learning Outcomes -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Learning Outcomes <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"learning-outcomes-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Features <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"features-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
            </div>
        `;
        \$('#modules-container').append(newModule);
        moduleIndex++;
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        // Renumber modules
        \$('#modules-container .module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
        });
        toggleModuleValidation();
    });

    // Enhanced field styling
    \$('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        \$(this).css('border-color', '#007bff');
        \$(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        \$(this).css('border-color', '#ced4da');
        \$(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    \$('[data-bs-toggle=\"tooltip\"]').tooltip();
});
</script>
{% endblock %}
", "admin/courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\edit.html.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* components/admin_page_layout.html.twig */
class __TwigTemplate_a840f26679537084d301cea3add64a2f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
            'modals' => [$this, 'block_modals'],
            'page_scripts' => [$this, 'block_page_scripts'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_page_layout.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_page_layout.html.twig"));

        // line 21
        yield "
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 24
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 24, $this->source); })()), "flashes", ["success"], "method", false, false, false, 24));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 25
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 26
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 30
        yield "
    ";
        // line 31
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 31, $this->source); })()), "flashes", ["error"], "method", false, false, false, 31));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 32
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 33
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 37
        yield "
    <!-- Standardized Header with Table Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        ";
        // line 44
        if (array_key_exists("page_icon", $context)) {
            // line 45
            yield "                            <i class=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_icon"]) || array_key_exists("page_icon", $context) ? $context["page_icon"] : (function () { throw new RuntimeError('Variable "page_icon" does not exist.', 45, $this->source); })()), "html", null, true);
            yield " mr-3\" style=\"font-size: 2rem;\"></i>
                        ";
        }
        // line 47
        yield "                        ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("page_title", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["page_title"]) || array_key_exists("page_title", $context) ? $context["page_title"] : (function () { throw new RuntimeError('Variable "page_title" does not exist.', 47, $this->source); })()), "Admin Page")) : ("Admin Page")), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Professional Search -->
                        <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                            <div class=\"input-group\" style=\"width: 320px;\">
                                <input type=\"text\"
                                       id=\"professional-search\"
                                       class=\"form-control form-control-lg admin-search-input\"
                                       placeholder=\"";
        // line 58
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("search_placeholder", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["search_placeholder"]) || array_key_exists("search_placeholder", $context) ? $context["search_placeholder"] : (function () { throw new RuntimeError('Variable "search_placeholder" does not exist.', 58, $this->source); })()), "Search...")) : ("Search...")), "html", null, true);
        yield "\"
                                       style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\"
                                            class=\"btn btn-lg admin-search-btn\"
                                            id=\"search-clear-btn\"
                                            style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                        <i class=\"fas fa-search\"></i>
                                    </button>
                                </div>
                            </div>
                            <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                        </div>

                        <!-- Additional Buttons -->
                        ";
        // line 73
        if (array_key_exists("additional_buttons", $context)) {
            // line 74
            yield "                            ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["additional_buttons"]) || array_key_exists("additional_buttons", $context) ? $context["additional_buttons"] : (function () { throw new RuntimeError('Variable "additional_buttons" does not exist.', 74, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["button"]) {
                // line 75
                yield "                                <a href=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "url", [], "any", false, false, false, 75), "html", null, true);
                yield "\"
                                   class=\"btn ";
                // line 76
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["button"], "class", [], "any", true, true, false, 76)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "class", [], "any", false, false, false, 76), "btn-outline-light")) : ("btn-outline-light")), "html", null, true);
                yield " me-2 mb-2 mb-md-0\"
                                   style=\"";
                // line 77
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["button"], "style", [], "any", true, true, false, 77)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "style", [], "any", false, false, false, 77), "")) : ("")), "html", null, true);
                yield "\"
                                   ";
                // line 78
                if (CoreExtension::getAttribute($this->env, $this->source, $context["button"], "title", [], "any", true, true, false, 78)) {
                    yield "title=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "title", [], "any", false, false, false, 78), "html", null, true);
                    yield "\"";
                }
                yield ">
                                    ";
                // line 79
                if (CoreExtension::getAttribute($this->env, $this->source, $context["button"], "icon", [], "any", true, true, false, 79)) {
                    yield "<i class=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "icon", [], "any", false, false, false, 79), "html", null, true);
                    yield " ";
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["button"], "text", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        yield "me-2";
                    }
                    yield "\"></i>";
                }
                // line 80
                yield "                                    ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "text", [], "any", false, false, false, 80), "html", null, true);
                yield "
                                </a>
                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['button'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 83
            yield "                        ";
        }
        // line 84
        yield "
                        <!-- Export CSV Button -->
                        ";
        // line 86
        if (array_key_exists("export_button", $context)) {
            // line 87
            yield "                            <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["export_button"]) || array_key_exists("export_button", $context) ? $context["export_button"] : (function () { throw new RuntimeError('Variable "export_button" does not exist.', 87, $this->source); })()), "url", [], "any", false, false, false, 87), "html", null, true);
            yield "\"
                               class=\"btn me-2 mb-2 mb-md-0\"
                               style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #007bff; border: 2px solid #007bff; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;\"
                               onmouseover=\"this.style.background='#007bff'; this.style.color='white';\"
                               onmouseout=\"this.style.background='white'; this.style.color='#007bff';\"
                               title=\"";
            // line 92
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["export_button"] ?? null), "title", [], "any", true, true, false, 92)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["export_button"]) || array_key_exists("export_button", $context) ? $context["export_button"] : (function () { throw new RuntimeError('Variable "export_button" does not exist.', 92, $this->source); })()), "title", [], "any", false, false, false, 92), "Export CSV")) : ("Export CSV")), "html", null, true);
            yield "\">
                                <i class=\"";
            // line 93
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["export_button"] ?? null), "icon", [], "any", true, true, false, 93)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["export_button"]) || array_key_exists("export_button", $context) ? $context["export_button"] : (function () { throw new RuntimeError('Variable "export_button" does not exist.', 93, $this->source); })()), "icon", [], "any", false, false, false, 93), "fas fa-download")) : ("fas fa-download")), "html", null, true);
            yield "\" style=\"font-size: 1.1rem;\"></i>
                            </a>
                        ";
        }
        // line 96
        yield "
                        <!-- Create Button -->
                        ";
        // line 98
        if (array_key_exists("create_button", $context)) {
            // line 99
            yield "                            <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 99, $this->source); })()), "url", [], "any", false, false, false, 99), "html", null, true);
            yield "\"
                               class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                               style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                                ";
            // line 102
            if (CoreExtension::getAttribute($this->env, $this->source, ($context["create_button"] ?? null), "icon", [], "any", true, true, false, 102)) {
                yield "<i class=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 102, $this->source); })()), "icon", [], "any", false, false, false, 102), "html", null, true);
                yield " me-2\"></i>";
            }
            // line 103
            yield "                                ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 103, $this->source); })()), "text", [], "any", false, false, false, 103), "html", null, true);
            yield "
                            </a>
                        ";
        }
        // line 106
        yield "                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        ";
        // line 112
        if ((array_key_exists("stats", $context) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 112, $this->source); })())) > 0))) {
            // line 113
            yield "        <div class=\"card-body pb-0\">
            <div class=\"row mb-4\">
                ";
            // line 115
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 115, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["stat"]) {
                // line 116
                yield "                <div class=\"col-xl-3 col-md-6 mb-3\">
                    <div class=\"card border-0 shadow-sm admin-stat-card\">
                        <div class=\"card-body\">
                            <div class=\"d-flex align-items-center\">
                                <div class=\"admin-stat-icon me-3\"
                                     style=\"background: ";
                // line 121
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "gradient", [], "any", true, true, false, 121)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "gradient", [], "any", false, false, false, 121), "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)")) : ("linear-gradient(135deg, #011a2d 0%, #1a3461 100%)")), "html", null, true);
                yield ";\">
                                    <i class=\"";
                // line 122
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "icon", [], "any", true, true, false, 122)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "icon", [], "any", false, false, false, 122), "fas fa-chart-bar")) : ("fas fa-chart-bar")), "html", null, true);
                yield " text-white\"></i>
                                </div>
                                <div>
                                    <h6 class=\"text-muted mb-1\">";
                // line 125
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "title", [], "any", false, false, false, 125), "html", null, true);
                yield "</h6>
                                    <h4 class=\"mb-0\" style=\"color: ";
                // line 126
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "color", [], "any", true, true, false, 126)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "color", [], "any", false, false, false, 126), "#011a2d")) : ("#011a2d")), "html", null, true);
                yield ";\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["stat"], "value", [], "any", false, false, false, 126), "html", null, true);
                yield "</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['stat'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 133
            yield "            </div>
        </div>
        ";
        }
        // line 136
        yield "
        <!-- Table Content Area -->
        <div class=\"card-body pt-0\">
            ";
        // line 139
        yield from $this->unwrap()->yieldBlock('table_content', $context, $blocks);
        // line 142
        yield "        </div>
    </div>
</div>

<!-- Professional Confirmation Modals -->
";
        // line 147
        yield from $this->unwrap()->yieldBlock('modals', $context, $blocks);
        // line 204
        yield "
";
        // line 205
        yield from $this->unwrap()->yieldBlock('page_scripts', $context, $blocks);
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    // line 139
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 140
        yield "                <!-- Table content will be inserted here -->
            ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 147
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_modals(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "modals"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "modals"));

        // line 148
        yield "    <!-- Status Toggle Confirmation Modal -->
    <div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"statusToggleModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-toggle-on me-2\"></i>Confirm Status Change
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #011a2d;\">
                        Are you sure you want to <span id=\"statusAction\">change</span> \"<span id=\"itemTitle\">Item Name</span>\"?
                    </p>
                    <small class=\"text-muted\">This action will change the item's status immediately.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-times me-1\"></i>Cancel
                    </button>
                    <button type=\"button\" class=\"btn btn-sm\" id=\"confirmStatusToggle\" style=\"background: #011a2d; color: white; border: none; transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"deleteConfirmModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-trash me-2\"></i>Confirm Deletion
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        Are you sure you want to delete \"<span id=\"deleteItemTitle\">Item Name</span>\"?
                    </p>
                    <small class=\"text-muted\">This action cannot be undone.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-times me-1\"></i>Cancel
                    </button>
                    <button type=\"button\" class=\"btn btn-sm\" id=\"confirmDelete\" style=\"background: #a90418; color: white; border: none; transition: all 0.3s ease;\">
                        <i class=\"fas fa-trash me-1\"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 205
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_scripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_scripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_scripts"));

        // line 206
        yield "<script>
// Unified Admin Page JavaScript Utilities
class AdminPageUtils {
    static initializeSearch(searchInputId, rowClass, searchFields) {
        const searchInput = document.querySelector(searchInputId);
        const searchBtn = document.querySelector('#search-clear-btn');
        const resultsCount = document.querySelector('#search-results-count');
        const rows = document.querySelectorAll(rowClass);

        let searchTimeout;

        if (!searchInput) return;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                AdminPageUtils.performSearch(this.value.toLowerCase(), rows, searchFields, searchBtn, resultsCount);
            }, 300);
        });

        searchBtn.addEventListener('click', function() {
            if (searchInput.value) {
                searchInput.value = '';
                AdminPageUtils.performSearch('', rows, searchFields, searchBtn, resultsCount);
                searchInput.focus();
            }
        });
    }

    static performSearch(query, rows, searchFields, searchBtn, resultsCount) {
        let visibleCount = 0;

        rows.forEach(row => {
            let isVisible = false;
            
            if (!query) {
                isVisible = true;
            } else {
                searchFields.forEach(field => {
                    const element = row.querySelector(field);
                    if (element && element.textContent.toLowerCase().includes(query)) {
                        isVisible = true;
                    }
                });
            }

            if (isVisible) {
                row.style.display = '';
                row.style.animation = 'fadeIn 0.3s ease-in';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Update search button icon
        if (query) {
            searchBtn.innerHTML = '<i class=\"fas fa-times\"></i>';
        } else {
            searchBtn.innerHTML = '<i class=\"fas fa-search\"></i>';
        }

        // Update results count
        if (resultsCount) {
            if (query) {
                resultsCount.textContent = `\${visibleCount} result\${visibleCount !== 1 ? 's' : ''} found`;
                resultsCount.style.display = 'block';
            } else {
                resultsCount.style.display = 'none';
            }
        }
    }

    static showStatusModal(itemId, itemTitle, currentStatus, actionCallback) {
        const action = currentStatus ? 'deactivate' : 'activate';
        document.getElementById('statusAction').textContent = action;
        document.getElementById('itemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmStatusToggle');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            actionCallback(itemId, !currentStatus);
        };

        new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
    }

    static showDeleteModal(itemId, itemTitle, deleteCallback) {
        document.getElementById('deleteItemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmDelete');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            deleteCallback(itemId);
        };

        new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
    }

    static showLoadingState() {
        // Create loading overlay if it doesn't exist
        let overlay = document.getElementById('adminLoadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'adminLoadingOverlay';
            overlay.className = 'admin-loading-overlay';
            overlay.innerHTML = `
                <div class=\"admin-loading-content\">
                    <div class=\"admin-loading-spinner\"></div>
                    <p class=\"admin-loading-text\">Processing...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }

    static hideLoadingState() {
        const overlay = document.getElementById('adminLoadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    static showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        document.querySelectorAll('.admin-notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.className = `admin-notification alert alert-\${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 350px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
        `;

        const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle';
        notification.innerHTML = `
            <div class=\"d-flex align-items-center\">
                <i class=\"fas fa-\${icon} me-2\"></i>
                <span>\${message}</span>
                <button type=\"button\" class=\"btn-close ms-auto\" onclick=\"this.parentElement.parentElement.remove()\"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, duration);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .admin-stat-card {
        transition: all 0.3s ease;
    }
    
    .admin-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .admin-stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .admin-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(1, 26, 45, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .admin-loading-content {
        text-align: center;
        color: white;
    }

    .admin-loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: adminSpin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    .admin-loading-text {
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0;
    }

    @keyframes adminSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .admin-notification {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Make AdminPageUtils globally available
window.AdminPageUtils = AdminPageUtils;
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "components/admin_page_layout.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  451 => 206,  438 => 205,  372 => 148,  359 => 147,  347 => 140,  334 => 139,  323 => 205,  320 => 204,  318 => 147,  311 => 142,  309 => 139,  304 => 136,  299 => 133,  284 => 126,  280 => 125,  274 => 122,  270 => 121,  263 => 116,  259 => 115,  255 => 113,  253 => 112,  245 => 106,  238 => 103,  232 => 102,  225 => 99,  223 => 98,  219 => 96,  213 => 93,  209 => 92,  200 => 87,  198 => 86,  194 => 84,  191 => 83,  181 => 80,  171 => 79,  163 => 78,  159 => 77,  155 => 76,  150 => 75,  145 => 74,  143 => 73,  125 => 58,  110 => 47,  104 => 45,  102 => 44,  93 => 37,  83 => 33,  80 => 32,  76 => 31,  73 => 30,  63 => 26,  60 => 25,  56 => 24,  51 => 21,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{#
    Capitol Academy Unified Admin Page Layout Component
    Standardized layout for all admin pages with consistent styling and functionality

    Parameters:
    - page_title: Main page title
    - page_icon: FontAwesome icon for the page
    - create_button: Object with {url, text, icon} for create button
    - search_placeholder: Placeholder text for search input
    - stats: Array of stat objects with {title, value, icon, color, gradient}
    - table_config: Object with table configuration
    - additional_buttons: Array of additional header buttons

    Enhanced with Capitol Academy design standards:
    - Professional card-based layout with shadow effects
    - Brand color scheme (#011a2d navy, #a90418 red, white)
    - Consistent button styling with gradients
    - Responsive design with Bootstrap 5
    - Smooth 300ms transitions
#}

<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Standardized Header with Table Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        {% if page_icon is defined %}
                            <i class=\"{{ page_icon }} mr-3\" style=\"font-size: 2rem;\"></i>
                        {% endif %}
                        {{ page_title|default('Admin Page') }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Professional Search -->
                        <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                            <div class=\"input-group\" style=\"width: 320px;\">
                                <input type=\"text\"
                                       id=\"professional-search\"
                                       class=\"form-control form-control-lg admin-search-input\"
                                       placeholder=\"{{ search_placeholder|default('Search...') }}\"
                                       style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\"
                                            class=\"btn btn-lg admin-search-btn\"
                                            id=\"search-clear-btn\"
                                            style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                        <i class=\"fas fa-search\"></i>
                                    </button>
                                </div>
                            </div>
                            <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                        </div>

                        <!-- Additional Buttons -->
                        {% if additional_buttons is defined %}
                            {% for button in additional_buttons %}
                                <a href=\"{{ button.url }}\"
                                   class=\"btn {{ button.class|default('btn-outline-light') }} me-2 mb-2 mb-md-0\"
                                   style=\"{{ button.style|default('') }}\"
                                   {% if button.title is defined %}title=\"{{ button.title }}\"{% endif %}>
                                    {% if button.icon is defined %}<i class=\"{{ button.icon }} {% if button.text %}me-2{% endif %}\"></i>{% endif %}
                                    {{ button.text }}
                                </a>
                            {% endfor %}
                        {% endif %}

                        <!-- Export CSV Button -->
                        {% if export_button is defined %}
                            <a href=\"{{ export_button.url }}\"
                               class=\"btn me-2 mb-2 mb-md-0\"
                               style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #007bff; border: 2px solid #007bff; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;\"
                               onmouseover=\"this.style.background='#007bff'; this.style.color='white';\"
                               onmouseout=\"this.style.background='white'; this.style.color='#007bff';\"
                               title=\"{{ export_button.title|default('Export CSV') }}\">
                                <i class=\"{{ export_button.icon|default('fas fa-download') }}\" style=\"font-size: 1.1rem;\"></i>
                            </a>
                        {% endif %}

                        <!-- Create Button -->
                        {% if create_button is defined %}
                            <a href=\"{{ create_button.url }}\"
                               class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                               style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                                {% if create_button.icon is defined %}<i class=\"{{ create_button.icon }} me-2\"></i>{% endif %}
                                {{ create_button.text }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        {% if stats is defined and stats|length > 0 %}
        <div class=\"card-body pb-0\">
            <div class=\"row mb-4\">
                {% for stat in stats %}
                <div class=\"col-xl-3 col-md-6 mb-3\">
                    <div class=\"card border-0 shadow-sm admin-stat-card\">
                        <div class=\"card-body\">
                            <div class=\"d-flex align-items-center\">
                                <div class=\"admin-stat-icon me-3\"
                                     style=\"background: {{ stat.gradient|default('linear-gradient(135deg, #011a2d 0%, #1a3461 100%)') }};\">
                                    <i class=\"{{ stat.icon|default('fas fa-chart-bar') }} text-white\"></i>
                                </div>
                                <div>
                                    <h6 class=\"text-muted mb-1\">{{ stat.title }}</h6>
                                    <h4 class=\"mb-0\" style=\"color: {{ stat.color|default('#011a2d') }};\">{{ stat.value }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Table Content Area -->
        <div class=\"card-body pt-0\">
            {% block table_content %}
                <!-- Table content will be inserted here -->
            {% endblock %}
        </div>
    </div>
</div>

<!-- Professional Confirmation Modals -->
{% block modals %}
    <!-- Status Toggle Confirmation Modal -->
    <div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"statusToggleModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-toggle-on me-2\"></i>Confirm Status Change
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #011a2d;\">
                        Are you sure you want to <span id=\"statusAction\">change</span> \"<span id=\"itemTitle\">Item Name</span>\"?
                    </p>
                    <small class=\"text-muted\">This action will change the item's status immediately.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-times me-1\"></i>Cancel
                    </button>
                    <button type=\"button\" class=\"btn btn-sm\" id=\"confirmStatusToggle\" style=\"background: #011a2d; color: white; border: none; transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"deleteConfirmModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-trash me-2\"></i>Confirm Deletion
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        Are you sure you want to delete \"<span id=\"deleteItemTitle\">Item Name</span>\"?
                    </p>
                    <small class=\"text-muted\">This action cannot be undone.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-times me-1\"></i>Cancel
                    </button>
                    <button type=\"button\" class=\"btn btn-sm\" id=\"confirmDelete\" style=\"background: #a90418; color: white; border: none; transition: all 0.3s ease;\">
                        <i class=\"fas fa-trash me-1\"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block page_scripts %}
<script>
// Unified Admin Page JavaScript Utilities
class AdminPageUtils {
    static initializeSearch(searchInputId, rowClass, searchFields) {
        const searchInput = document.querySelector(searchInputId);
        const searchBtn = document.querySelector('#search-clear-btn');
        const resultsCount = document.querySelector('#search-results-count');
        const rows = document.querySelectorAll(rowClass);

        let searchTimeout;

        if (!searchInput) return;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                AdminPageUtils.performSearch(this.value.toLowerCase(), rows, searchFields, searchBtn, resultsCount);
            }, 300);
        });

        searchBtn.addEventListener('click', function() {
            if (searchInput.value) {
                searchInput.value = '';
                AdminPageUtils.performSearch('', rows, searchFields, searchBtn, resultsCount);
                searchInput.focus();
            }
        });
    }

    static performSearch(query, rows, searchFields, searchBtn, resultsCount) {
        let visibleCount = 0;

        rows.forEach(row => {
            let isVisible = false;
            
            if (!query) {
                isVisible = true;
            } else {
                searchFields.forEach(field => {
                    const element = row.querySelector(field);
                    if (element && element.textContent.toLowerCase().includes(query)) {
                        isVisible = true;
                    }
                });
            }

            if (isVisible) {
                row.style.display = '';
                row.style.animation = 'fadeIn 0.3s ease-in';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Update search button icon
        if (query) {
            searchBtn.innerHTML = '<i class=\"fas fa-times\"></i>';
        } else {
            searchBtn.innerHTML = '<i class=\"fas fa-search\"></i>';
        }

        // Update results count
        if (resultsCount) {
            if (query) {
                resultsCount.textContent = `\${visibleCount} result\${visibleCount !== 1 ? 's' : ''} found`;
                resultsCount.style.display = 'block';
            } else {
                resultsCount.style.display = 'none';
            }
        }
    }

    static showStatusModal(itemId, itemTitle, currentStatus, actionCallback) {
        const action = currentStatus ? 'deactivate' : 'activate';
        document.getElementById('statusAction').textContent = action;
        document.getElementById('itemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmStatusToggle');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            actionCallback(itemId, !currentStatus);
        };

        new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
    }

    static showDeleteModal(itemId, itemTitle, deleteCallback) {
        document.getElementById('deleteItemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmDelete');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            deleteCallback(itemId);
        };

        new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
    }

    static showLoadingState() {
        // Create loading overlay if it doesn't exist
        let overlay = document.getElementById('adminLoadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'adminLoadingOverlay';
            overlay.className = 'admin-loading-overlay';
            overlay.innerHTML = `
                <div class=\"admin-loading-content\">
                    <div class=\"admin-loading-spinner\"></div>
                    <p class=\"admin-loading-text\">Processing...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }

    static hideLoadingState() {
        const overlay = document.getElementById('adminLoadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    static showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        document.querySelectorAll('.admin-notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.className = `admin-notification alert alert-\${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 350px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
        `;

        const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle';
        notification.innerHTML = `
            <div class=\"d-flex align-items-center\">
                <i class=\"fas fa-\${icon} me-2\"></i>
                <span>\${message}</span>
                <button type=\"button\" class=\"btn-close ms-auto\" onclick=\"this.parentElement.parentElement.remove()\"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, duration);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .admin-stat-card {
        transition: all 0.3s ease;
    }
    
    .admin-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .admin-stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .admin-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(1, 26, 45, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .admin-loading-content {
        text-align: center;
        color: white;
    }

    .admin-loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: adminSpin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    .admin-loading-text {
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0;
    }

    @keyframes adminSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .admin-notification {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Make AdminPageUtils globally available
window.AdminPageUtils = AdminPageUtils;
</script>
{% endblock %}
", "components/admin_page_layout.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\components\\admin_page_layout.html.twig");
    }
}

<?php

namespace ContainerR8yDayf;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_JqHm011Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.jqHm011' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.jqHm011'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'course' => ['privates', '.errored..service_locator.jqHm011.App\\Entity\\Course', NULL, 'Cannot autowire service ".service_locator.jqHm011": it needs an instance of "App\\Entity\\Course" but this type has been excluded in "config/services.yaml".'],
        ], [
            'course' => 'App\\Entity\\Course',
        ]);
    }
}

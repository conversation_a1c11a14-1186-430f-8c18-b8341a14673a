{% extends 'admin/base.html.twig' %}

{% block title %}Instructor Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Instructor Management{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Instructors Management',
    'page_icon': 'fas fa-chalkboard-teacher',
    'search_placeholder': 'Search instructors by name, email, or specialization...',
    'create_button': {
        'url': path('admin_instructor_new'),
        'text': 'Add New Instructor',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Instructors',
            'value': instructors|length,
            'icon': 'fas fa-chalkboard-teacher',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': instructors|filter(instructor => instructor.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': instructors|filter(instructor => not instructor.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': instructors|filter(instructor => instructor.createdAt and instructor.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Photo'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Specialization'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for instructor in instructors %}
            {% set row_cells = [
                {
                    'content': instructor.profileImage ?
                        '<img src="' ~ asset('uploads/instructors/' ~ instructor.profileImage) ~ '" alt="' ~ instructor.name ~ '" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;" onerror="this.src=\'' ~ asset('images/instructors/instructor-default-pp.png') ~ '\'">' :
                        '<div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;">' ~ instructor.name|slice(0,1)|upper ~ '</div>'
                },
                {
                    'content': '<h6 class="instructor-name mb-0 font-weight-bold text-dark">' ~ instructor.name ~ '</h6>'
                },
                {
                    'content': instructor.email ?
                        '<a href="mailto:' ~ instructor.email ~ '" class="instructor-email text-decoration-none" style="color: #011a2d; font-weight: 500;">' ~ instructor.email ~ '</a>' :
                        '<span class="text-muted">N/A</span>'
                },
                {
                    'content': instructor.specialization ?
                        '<span class="badge instructor-specialization" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; padding: 0.4rem 0.6rem; border-radius: 6px;">' ~ instructor.specialization ~ '</span>' :
                        '<span class="text-muted instructor-specialization">Not specified</span>'
                },
                {
                    'content': instructor.isActive ?
                        '<span class="badge" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>' :
                        '<span class="badge" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-pause-circle mr-1"></i> Inactive</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_instructor_show', {'emailPrefix': instructor.emailPrefix}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Instructor"><i class="fas fa-eye"></i></a>
                        <a href="' ~ path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Instructor"><i class="fas fa-edit"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, ' ~ (instructor.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (instructor.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (instructor.isActive ? 'Deactivate' : 'Activate') ~ ' Instructor" onclick="toggleInstructorStatus(' ~ instructor.id ~ ', \'' ~ instructor.name ~ '\', ' ~ instructor.isActive ~ ')"><i class="fas fa-' ~ (instructor.isActive ? 'pause' : 'play') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Instructor" onclick="deleteInstructor(' ~ instructor.id ~ ', \'' ~ instructor.name ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'instructor-row',
            'empty_message': 'No instructors found',
            'empty_icon': 'fas fa-chalkboard-teacher',
            'empty_description': 'Get started by adding your first instructor.',
            'search_config': {
                'fields': ['.instructor-name', '.instructor-email', '.instructor-specialization']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.instructor-row',
        ['.instructor-name', '.instructor-email', '.instructor-specialization']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Instructor management functions
function toggleInstructorStatus(instructorId, instructorName, isActive) {
    showStatusModal(instructorName, isActive, function() {
        executeInstructorStatusToggle(instructorId);
    });
}

function deleteInstructor(instructorId, instructorName) {
    showDeleteModal(instructorName, function() {
        executeInstructorDelete(instructorId);
    });
}

// Actual execution functions
function executeInstructorStatusToggle(instructorId) {
    fetch(`/admin/instructor/${instructorId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the instructor status');
    });
}

function executeInstructorDelete(instructorId) {
    fetch(`/admin/instructor/${instructorId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the instructor');
    });
}
</script>
{% endblock %}

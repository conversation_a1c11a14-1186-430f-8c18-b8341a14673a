<?php

namespace App\Entity;

use App\Repository\PromotionalBannerRepository;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: PromotionalBannerRepository::class)]
class PromotionalBanner
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Title is required')]
    #[Assert\Length(max: 255, maxMessage: 'Title cannot be longer than 255 characters')]
    private ?string $title = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $slug = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\Length(max: 1000, maxMessage: 'Description cannot be longer than 1000 characters')]
    private ?string $description = null;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $endDate = null;



    #[ORM\Column]
    private bool $isActive = true;

    #[ORM\Column(length: 7, nullable: true)]
    #[Assert\Regex(pattern: '/^#[0-9A-Fa-f]{6}$/', message: 'Please enter a valid hex color code')]
    private ?string $backgroundColor = '#001427';

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getEndDate(): ?\DateTimeImmutable
    {
        return $this->endDate;
    }

    public function setEndDate(?\DateTimeImmutable $endDate): static
    {
        $this->endDate = $endDate;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }



    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getBackgroundColor(): ?string
    {
        return $this->backgroundColor;
    }

    public function setBackgroundColor(?string $backgroundColor): static
    {
        $this->backgroundColor = $backgroundColor;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * Check if the banner is currently valid (active and not expired)
     */
    public function isCurrentlyValid(): bool
    {
        if (!$this->isActive) {
            return false;
        }

        if ($this->endDate && $this->endDate < new \DateTimeImmutable()) {
            return false;
        }

        return true;
    }

    /**
     * Get time remaining until end date in seconds
     */
    public function getTimeRemainingSeconds(): ?int
    {
        if (!$this->endDate) {
            return null;
        }

        $now = new \DateTimeImmutable();
        if ($this->endDate < $now) {
            return 0;
        }

        return $this->endDate->getTimestamp() - $now->getTimestamp();
    }

    /**
     * Get formatted time remaining as array
     */
    public function getFormattedTimeRemaining(): ?array
    {
        $seconds = $this->getTimeRemainingSeconds();
        
        if ($seconds === null || $seconds <= 0) {
            return null;
        }

        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return [
            'days' => str_pad((string)$days, 2, '0', STR_PAD_LEFT),
            'hours' => str_pad((string)$hours, 2, '0', STR_PAD_LEFT),
            'minutes' => str_pad((string)$minutes, 2, '0', STR_PAD_LEFT),
            'seconds' => str_pad((string)$remainingSeconds, 2, '0', STR_PAD_LEFT)
        ];
    }


}

<?php

namespace ContainerR8yDayf;

return [
    '.1_ServiceLocator~gNr3Q2B' => true,
    '.App\\Command\\CreateMasterAdminCommand.lazy' => true,
    '.App\\Command\\CreateTestUsersAndReviewsCommand.lazy' => true,
    '.App\\Command\\ImportCoursesCommand.lazy' => true,
    '.Psr\\Container\\ContainerInterface $parameter_bag' => true,
    '.Psr\\Log\\LoggerInterface $asset_mapperLogger' => true,
    '.Psr\\Log\\LoggerInterface $http_clientLogger' => true,
    '.abstract.instanceof.App\\Command\\CreateMasterAdminCommand' => true,
    '.abstract.instanceof.App\\Command\\CreateTestUsersAndReviewsCommand' => true,
    '.abstract.instanceof.App\\Command\\ImportCoursesCommand' => true,
    '.abstract.instanceof.App\\Controller\\AdminController' => true,
    '.abstract.instanceof.App\\Controller\\AdminInstructorController' => true,
    '.abstract.instanceof.App\\Controller\\AdminMarketAnalysisController' => true,
    '.abstract.instanceof.App\\Controller\\AdminSecurityController' => true,
    '.abstract.instanceof.App\\Controller\\Admin\\CategoryController' => true,
    '.abstract.instanceof.App\\Controller\\Admin\\DashboardController' => true,
    '.abstract.instanceof.App\\Controller\\Admin\\OrderController' => true,
    '.abstract.instanceof.App\\Controller\\Admin\\VideoController' => true,
    '.abstract.instanceof.App\\Controller\\CartController' => true,
    '.abstract.instanceof.App\\Controller\\CheckoutController' => true,
    '.abstract.instanceof.App\\Controller\\ContactController' => true,
    '.abstract.instanceof.App\\Controller\\CourseController' => true,
    '.abstract.instanceof.App\\Controller\\GoogleAuthController' => true,
    '.abstract.instanceof.App\\Controller\\HomeController' => true,
    '.abstract.instanceof.App\\Controller\\MarketAnalysisController' => true,
    '.abstract.instanceof.App\\Controller\\PasswordResetController' => true,
    '.abstract.instanceof.App\\Controller\\PaymentController' => true,
    '.abstract.instanceof.App\\Controller\\SearchController' => true,
    '.abstract.instanceof.App\\Controller\\SecurityController' => true,
    '.abstract.instanceof.App\\Controller\\UserController' => true,
    '.abstract.instanceof.App\\Controller\\UserOrderController' => true,
    '.abstract.instanceof.App\\Controller\\VideoController' => true,
    '.abstract.instanceof.App\\DataFixtures\\AppFixtures' => true,
    '.abstract.instanceof.App\\DataFixtures\\CourseFixtures' => true,
    '.abstract.instanceof.App\\DataFixtures\\InstructorFixtures' => true,
    '.abstract.instanceof.App\\DataFixtures\\MarketAnalysisFixtures' => true,
    '.abstract.instanceof.App\\Form\\AdminFormType' => true,
    '.abstract.instanceof.App\\Form\\CategoryType' => true,
    '.abstract.instanceof.App\\Form\\ContactType' => true,
    '.abstract.instanceof.App\\Form\\CourseType' => true,
    '.abstract.instanceof.App\\Form\\InstructorType' => true,
    '.abstract.instanceof.App\\Form\\LoginFormType' => true,
    '.abstract.instanceof.App\\Form\\MarketAnalysisType' => true,
    '.abstract.instanceof.App\\Form\\PartnerType' => true,
    '.abstract.instanceof.App\\Form\\PlanType' => true,
    '.abstract.instanceof.App\\Form\\PromotionalBannerType' => true,
    '.abstract.instanceof.App\\Form\\RegistrationFormType' => true,
    '.abstract.instanceof.App\\Form\\VideoType' => true,
    '.abstract.instanceof.App\\Repository\\AdminRepository' => true,
    '.abstract.instanceof.App\\Repository\\CategoryRepository' => true,
    '.abstract.instanceof.App\\Repository\\CertificationRepository' => true,
    '.abstract.instanceof.App\\Repository\\ContactRepository' => true,
    '.abstract.instanceof.App\\Repository\\CountryRepository' => true,
    '.abstract.instanceof.App\\Repository\\CourseModuleRepository' => true,
    '.abstract.instanceof.App\\Repository\\CourseRepository' => true,
    '.abstract.instanceof.App\\Repository\\CourseReviewRepository' => true,
    '.abstract.instanceof.App\\Repository\\EnrollmentRepository' => true,
    '.abstract.instanceof.App\\Repository\\InstructorRepository' => true,
    '.abstract.instanceof.App\\Repository\\MarketAnalysisRepository' => true,
    '.abstract.instanceof.App\\Repository\\OrderRepository' => true,
    '.abstract.instanceof.App\\Repository\\PartnerRepository' => true,
    '.abstract.instanceof.App\\Repository\\PasswordResetTokenRepository' => true,
    '.abstract.instanceof.App\\Repository\\PaymentRepository' => true,
    '.abstract.instanceof.App\\Repository\\PlanRepository' => true,
    '.abstract.instanceof.App\\Repository\\PromotionalBannerRepository' => true,
    '.abstract.instanceof.App\\Repository\\UserRepository' => true,
    '.abstract.instanceof.App\\Repository\\UserVideoAccessRepository' => true,
    '.abstract.instanceof.App\\Repository\\VideoRepository' => true,
    '.abstract.instanceof.App\\Twig\\AssetExtension' => true,
    '.abstract.instanceof.App\\Twig\\ContentExtension' => true,
    '.abstract.instanceof.App\\Twig\\PartnerExtension' => true,
    '.abstract.instanceof.App\\Twig\\PromotionalBannerExtension' => true,
    '.asset_mapper.command.compile.lazy' => true,
    '.asset_mapper.command.debug.lazy' => true,
    '.asset_mapper.importmap.command.audit.lazy' => true,
    '.asset_mapper.importmap.command.install.lazy' => true,
    '.asset_mapper.importmap.command.outdated.lazy' => true,
    '.asset_mapper.importmap.command.remove.lazy' => true,
    '.asset_mapper.importmap.command.require.lazy' => true,
    '.asset_mapper.importmap.command.update.lazy' => true,
    '.cache_connection.GD_MSZC' => true,
    '.cache_connection.JKE6keX' => true,
    '.console.command.about.lazy' => true,
    '.console.command.assets_install.lazy' => true,
    '.console.command.cache_clear.lazy' => true,
    '.console.command.cache_pool_clear.lazy' => true,
    '.console.command.cache_pool_delete.lazy' => true,
    '.console.command.cache_pool_invalidate_tags.lazy' => true,
    '.console.command.cache_pool_list.lazy' => true,
    '.console.command.cache_pool_prune.lazy' => true,
    '.console.command.cache_warmup.lazy' => true,
    '.console.command.config_debug.lazy' => true,
    '.console.command.config_dump_reference.lazy' => true,
    '.console.command.container_debug.lazy' => true,
    '.console.command.container_lint.lazy' => true,
    '.console.command.debug_autowiring.lazy' => true,
    '.console.command.dotenv_debug.lazy' => true,
    '.console.command.event_dispatcher_debug.lazy' => true,
    '.console.command.form_debug.lazy' => true,
    '.console.command.mailer_test.lazy' => true,
    '.console.command.messenger_consume_messages.lazy' => true,
    '.console.command.messenger_debug.lazy' => true,
    '.console.command.messenger_failed_messages_remove.lazy' => true,
    '.console.command.messenger_failed_messages_retry.lazy' => true,
    '.console.command.messenger_failed_messages_show.lazy' => true,
    '.console.command.messenger_setup_transports.lazy' => true,
    '.console.command.messenger_stats.lazy' => true,
    '.console.command.messenger_stop_workers.lazy' => true,
    '.console.command.router_debug.lazy' => true,
    '.console.command.router_match.lazy' => true,
    '.console.command.secrets_decrypt_to_local.lazy' => true,
    '.console.command.secrets_encrypt_from_local.lazy' => true,
    '.console.command.secrets_generate_key.lazy' => true,
    '.console.command.secrets_list.lazy' => true,
    '.console.command.secrets_remove.lazy' => true,
    '.console.command.secrets_set.lazy' => true,
    '.console.command.serializer_debug.lazy' => true,
    '.console.command.translation_debug.lazy' => true,
    '.console.command.translation_extract.lazy' => true,
    '.console.command.translation_pull.lazy' => true,
    '.console.command.translation_push.lazy' => true,
    '.console.command.validator_debug.lazy' => true,
    '.console.command.xliff_lint.lazy' => true,
    '.console.command.yaml_lint.lazy' => true,
    '.data_collector.command' => true,
    '.debug.http_client' => true,
    '.debug.http_client.inner' => true,
    '.debug.security.voter.security.access.authenticated_voter' => true,
    '.debug.security.voter.security.access.expression_voter' => true,
    '.debug.security.voter.security.access.simple_role_voter' => true,
    '.debug.serializer.encoder.serializer.encoder.csv' => true,
    '.debug.serializer.encoder.serializer.encoder.json' => true,
    '.debug.serializer.encoder.serializer.encoder.xml' => true,
    '.debug.serializer.encoder.serializer.encoder.yaml' => true,
    '.debug.serializer.normalizer.serializer.denormalizer.array' => true,
    '.debug.serializer.normalizer.serializer.denormalizer.unwrapping' => true,
    '.debug.serializer.normalizer.serializer.normalizer.backed_enum' => true,
    '.debug.serializer.normalizer.serializer.normalizer.constraint_violation_list' => true,
    '.debug.serializer.normalizer.serializer.normalizer.data_uri' => true,
    '.debug.serializer.normalizer.serializer.normalizer.dateinterval' => true,
    '.debug.serializer.normalizer.serializer.normalizer.datetime' => true,
    '.debug.serializer.normalizer.serializer.normalizer.datetimezone' => true,
    '.debug.serializer.normalizer.serializer.normalizer.flatten_exception' => true,
    '.debug.serializer.normalizer.serializer.normalizer.form_error' => true,
    '.debug.serializer.normalizer.serializer.normalizer.json_serializable' => true,
    '.debug.serializer.normalizer.serializer.normalizer.mime_message' => true,
    '.debug.serializer.normalizer.serializer.normalizer.object' => true,
    '.debug.serializer.normalizer.serializer.normalizer.problem' => true,
    '.debug.serializer.normalizer.serializer.normalizer.translatable' => true,
    '.debug.serializer.normalizer.serializer.normalizer.uid' => true,
    '.debug.value_resolver.argument_resolver.backed_enum_resolver' => true,
    '.debug.value_resolver.argument_resolver.datetime' => true,
    '.debug.value_resolver.argument_resolver.default' => true,
    '.debug.value_resolver.argument_resolver.not_tagged_controller' => true,
    '.debug.value_resolver.argument_resolver.query_parameter_value_resolver' => true,
    '.debug.value_resolver.argument_resolver.request' => true,
    '.debug.value_resolver.argument_resolver.request_attribute' => true,
    '.debug.value_resolver.argument_resolver.request_payload' => true,
    '.debug.value_resolver.argument_resolver.service' => true,
    '.debug.value_resolver.argument_resolver.session' => true,
    '.debug.value_resolver.argument_resolver.variadic' => true,
    '.debug.value_resolver.doctrine.orm.entity_value_resolver' => true,
    '.debug.value_resolver.security.security_token_value_resolver' => true,
    '.debug.value_resolver.security.user_value_resolver' => true,
    '.doctrine.orm.default_metadata_driver' => true,
    '.doctrine.orm.default_metadata_driver.inner' => true,
    '.doctrine_migrations.current_command.lazy' => true,
    '.doctrine_migrations.diff_command.lazy' => true,
    '.doctrine_migrations.dump_schema_command.lazy' => true,
    '.doctrine_migrations.execute_command.lazy' => true,
    '.doctrine_migrations.generate_command.lazy' => true,
    '.doctrine_migrations.latest_command.lazy' => true,
    '.doctrine_migrations.migrate_command.lazy' => true,
    '.doctrine_migrations.rollup_command.lazy' => true,
    '.doctrine_migrations.status_command.lazy' => true,
    '.doctrine_migrations.sync_metadata_command.lazy' => true,
    '.doctrine_migrations.up_to_date_command.lazy' => true,
    '.doctrine_migrations.version_command.lazy' => true,
    '.doctrine_migrations.versions_command.lazy' => true,
    '.errored..service_locator.4qavBNK.App\\Entity\\Instructor' => true,
    '.errored..service_locator.8IESKP1.App\\Entity\\Category' => true,
    '.errored..service_locator.8RUuq9s.App\\Entity\\Instructor' => true,
    '.errored..service_locator.Hz5btge.App\\Entity\\User' => true,
    '.errored..service_locator.WGDSHn2.App\\Entity\\MarketAnalysis' => true,
    '.errored..service_locator.YuEbyXL.App\\Entity\\Partner' => true,
    '.errored..service_locator.bki8G0J.App\\Entity\\Video' => true,
    '.errored..service_locator.jqHm011.App\\Entity\\Course' => true,
    '.errored..service_locator.kleoBdA.App\\Entity\\PromotionalBanner' => true,
    '.errored..service_locator.mCbvPMW.App\\Entity\\Admin' => true,
    '.errored..service_locator.ruxHxcA.App\\Entity\\Order' => true,
    '.errored..service_locator.y4_Zrx..Symfony\\Component\\Config\\Loader\\LoaderInterface' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\AdminRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CategoryRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CertificationRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\ContactRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CountryRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CourseModuleRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CourseRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CourseReviewRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\EnrollmentRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\InstructorRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\MarketAnalysisRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\OrderRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\PartnerRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\PasswordResetTokenRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\PaymentRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\PlanRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\PromotionalBannerRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\UserRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\UserVideoAccessRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\VideoRepository' => true,
    '.instanceof.Doctrine\\Bundle\\FixturesBundle\\ORMFixtureInterface.0.App\\DataFixtures\\AppFixtures' => true,
    '.instanceof.Doctrine\\Bundle\\FixturesBundle\\ORMFixtureInterface.0.App\\DataFixtures\\CourseFixtures' => true,
    '.instanceof.Doctrine\\Bundle\\FixturesBundle\\ORMFixtureInterface.0.App\\DataFixtures\\InstructorFixtures' => true,
    '.instanceof.Doctrine\\Bundle\\FixturesBundle\\ORMFixtureInterface.0.App\\DataFixtures\\MarketAnalysisFixtures' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AdminController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AdminInstructorController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AdminMarketAnalysisController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AdminSecurityController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\Admin\\CategoryController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\Admin\\DashboardController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\Admin\\OrderController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\Admin\\VideoController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CartController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CheckoutController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\ContactController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CourseController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\GoogleAuthController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\HomeController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\MarketAnalysisController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\PasswordResetController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\PaymentController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\SearchController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\SecurityController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\UserController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\UserOrderController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\VideoController' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\CreateMasterAdminCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\CreateTestUsersAndReviewsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\ImportCoursesCommand' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\AdminFormType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\CategoryType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\ContactType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\CourseType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\InstructorType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\LoginFormType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\MarketAnalysisType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\PartnerType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\PlanType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\PromotionalBannerType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\RegistrationFormType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.App\\Form\\VideoType' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AdminController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AdminInstructorController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AdminMarketAnalysisController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AdminSecurityController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\Admin\\CategoryController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\Admin\\DashboardController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\Admin\\OrderController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\Admin\\VideoController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CartController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CheckoutController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\ContactController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CourseController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\GoogleAuthController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\HomeController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\MarketAnalysisController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\PasswordResetController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\PaymentController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\SearchController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\SecurityController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\UserController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\UserOrderController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\VideoController' => true,
    '.instanceof.Twig\\Extension\\ExtensionInterface.0.App\\Twig\\AssetExtension' => true,
    '.instanceof.Twig\\Extension\\ExtensionInterface.0.App\\Twig\\ContentExtension' => true,
    '.instanceof.Twig\\Extension\\ExtensionInterface.0.App\\Twig\\PartnerExtension' => true,
    '.instanceof.Twig\\Extension\\ExtensionInterface.0.App\\Twig\\PromotionalBannerExtension' => true,
    '.lazy_profiler' => true,
    '.maker.auto_command.make_auth.lazy' => true,
    '.maker.auto_command.make_command.lazy' => true,
    '.maker.auto_command.make_controller.lazy' => true,
    '.maker.auto_command.make_crud.lazy' => true,
    '.maker.auto_command.make_docker_database.lazy' => true,
    '.maker.auto_command.make_entity.lazy' => true,
    '.maker.auto_command.make_fixtures.lazy' => true,
    '.maker.auto_command.make_form.lazy' => true,
    '.maker.auto_command.make_listener.lazy' => true,
    '.maker.auto_command.make_message.lazy' => true,
    '.maker.auto_command.make_messenger_middleware.lazy' => true,
    '.maker.auto_command.make_migration.lazy' => true,
    '.maker.auto_command.make_registration_form.lazy' => true,
    '.maker.auto_command.make_reset_password.lazy' => true,
    '.maker.auto_command.make_schedule.lazy' => true,
    '.maker.auto_command.make_security_custom.lazy' => true,
    '.maker.auto_command.make_security_form_login.lazy' => true,
    '.maker.auto_command.make_serializer_encoder.lazy' => true,
    '.maker.auto_command.make_serializer_normalizer.lazy' => true,
    '.maker.auto_command.make_stimulus_controller.lazy' => true,
    '.maker.auto_command.make_test.lazy' => true,
    '.maker.auto_command.make_twig_component.lazy' => true,
    '.maker.auto_command.make_twig_extension.lazy' => true,
    '.maker.auto_command.make_user.lazy' => true,
    '.maker.auto_command.make_validator.lazy' => true,
    '.maker.auto_command.make_voter.lazy' => true,
    '.maker.auto_command.make_webhook.lazy' => true,
    '.messenger.handler_descriptor.6kVvRT.' => true,
    '.messenger.handler_descriptor.Lml2ICs' => true,
    '.messenger.handler_descriptor.QXXNQ9d' => true,
    '.messenger.handler_descriptor.XZowc.T' => true,
    '.messenger.handler_descriptor.kEzMhfs' => true,
    '.messenger.handler_descriptor.p4Qvabm' => true,
    '.messenger.handler_descriptor.tGvt0LH' => true,
    '.messenger.handler_descriptor.vMw0m61' => true,
    '.monolog.command.server_log.lazy' => true,
    '.security.command.debug_firewall.lazy' => true,
    '.security.command.user_password_hash.lazy' => true,
    '.security.request_matcher.0.aTXGw' => true,
    '.security.request_matcher.1f3H6o2' => true,
    '.security.request_matcher.28NqKnb' => true,
    '.security.request_matcher.2z3VqqR' => true,
    '.security.request_matcher.5eRZPSi' => true,
    '.security.request_matcher.7smaLiy' => true,
    '.security.request_matcher.7uYTqQl' => true,
    '.security.request_matcher.C5hL7LM' => true,
    '.security.request_matcher.D5xVT_B' => true,
    '.security.request_matcher.I_Wy6An' => true,
    '.security.request_matcher.MtFG8cr' => true,
    '.security.request_matcher.Rjnkq2f' => true,
    '.security.request_matcher.X1FOpdX' => true,
    '.security.request_matcher.ZhKTXPj' => true,
    '.security.request_matcher.kLbKLHa' => true,
    '.security.request_matcher.ldbN158' => true,
    '.security.request_matcher.obhk0jm' => true,
    '.security.request_matcher.q1UFWmc' => true,
    '.security.request_matcher.unVmF7D' => true,
    '.security.request_matcher.xH2Lwlp' => true,
    '.security.request_matcher.xKZVXt1' => true,
    '.security.request_matcher.yrB7CAf' => true,
    '.security.request_matcher.zK5iVrC' => true,
    '.service_locator..Fs8Kd7' => true,
    '.service_locator..XI94lU' => true,
    '.service_locator.0TACwl3' => true,
    '.service_locator.1IiHida' => true,
    '.service_locator.2Z8Kpq5' => true,
    '.service_locator.4n4ylFv' => true,
    '.service_locator.4qavBNK' => true,
    '.service_locator.5cAhUFF' => true,
    '.service_locator.6lJWFv4' => true,
    '.service_locator.7NIAq8D' => true,
    '.service_locator.80edgLI' => true,
    '.service_locator.8IESKP1' => true,
    '.service_locator.8RUuq9s' => true,
    '.service_locator.9Idb4WS' => true,
    '.service_locator.9e2t_lq' => true,
    '.service_locator.9x8jmYi' => true,
    '.service_locator.AHldQnq' => true,
    '.service_locator.Ar70cHn' => true,
    '.service_locator.BEH0YgR' => true,
    '.service_locator.BFrsqsn' => true,
    '.service_locator.BQZP4Om' => true,
    '.service_locator.Bd5JDSL' => true,
    '.service_locator.BvvBT9K' => true,
    '.service_locator.C..ZzTu' => true,
    '.service_locator.CLe7xTy' => true,
    '.service_locator.CsMkqUa' => true,
    '.service_locator.EmZ4cZI' => true,
    '.service_locator.F9PKc.7' => true,
    '.service_locator.FeLY1aP' => true,
    '.service_locator.G7ToqKm' => true,
    '.service_locator.GXLTvxy' => true,
    '.service_locator.HRkK60W' => true,
    '.service_locator.Htq2ZV8' => true,
    '.service_locator.Hz5btge' => true,
    '.service_locator.I5Q3J1s' => true,
    '.service_locator.JRsg0Ca' => true,
    '.service_locator.JUqPTsr' => true,
    '.service_locator.JovALEP' => true,
    '.service_locator.KLVvNIq' => true,
    '.service_locator.Kl0E_Fe' => true,
    '.service_locator.L29W5HS' => true,
    '.service_locator.LD5oJC8' => true,
    '.service_locator.LKvMsLF' => true,
    '.service_locator.LM2GVnu' => true,
    '.service_locator.LcVn9Hr' => true,
    '.service_locator.LjXy5IJ' => true,
    '.service_locator.Mhqdd2r' => true,
    '.service_locator.NBUFN6A' => true,
    '.service_locator.NFOhFuc' => true,
    '.service_locator.O24_MAy' => true,
    '.service_locator.O2p6Lk7' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AdminController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AdminInstructorController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AdminMarketAnalysisController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AdminSecurityController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\Admin\\CategoryController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\Admin\\DashboardController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\Admin\\OrderController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\Admin\\VideoController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CartController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CheckoutController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\ContactController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CourseController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\GoogleAuthController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\HomeController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\MarketAnalysisController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\PasswordResetController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\PaymentController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\SearchController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\SecurityController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\UserController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\UserOrderController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\VideoController' => true,
    '.service_locator.OJmnvRJ' => true,
    '.service_locator.OgYe.wV' => true,
    '.service_locator.PF7xJ2w' => true,
    '.service_locator.Q1z.f9O' => true,
    '.service_locator.QPhu3Rh' => true,
    '.service_locator.QkVYtxV' => true,
    '.service_locator.Qyz7DBH' => true,
    '.service_locator.RIGJPw_' => true,
    '.service_locator.T_VdxJ4' => true,
    '.service_locator.Tjip78d' => true,
    '.service_locator.TpoC7U9' => true,
    '.service_locator.UWa3jiB' => true,
    '.service_locator.Uttg_Rw' => true,
    '.service_locator.VqrGy.V' => true,
    '.service_locator.W47vLMj' => true,
    '.service_locator.WGDSHn2' => true,
    '.service_locator.WZeIXfO' => true,
    '.service_locator.WjkTZZg' => true,
    '.service_locator.XXv1IfR' => true,
    '.service_locator.Xbsa8iG' => true,
    '.service_locator.Xf3Ro8W' => true,
    '.service_locator.XsmvmpH' => true,
    '.service_locator.Y4J.A.e' => true,
    '.service_locator.Y_TzhIc' => true,
    '.service_locator.Yc1mPPx' => true,
    '.service_locator.YuEbyXL' => true,
    '.service_locator.ZdZvBz9' => true,
    '.service_locator._kIAbz1' => true,
    '.service_locator._wyX1Dp' => true,
    '.service_locator.bJ.4HC5' => true,
    '.service_locator.bN.V_Nq' => true,
    '.service_locator.bN.V_Nq.router.default' => true,
    '.service_locator.bki8G0J' => true,
    '.service_locator.boi8aZg' => true,
    '.service_locator.c34Bris' => true,
    '.service_locator.c7f47p7' => true,
    '.service_locator.cUcW89y' => true,
    '.service_locator.cUcW89y.router.cache_warmer' => true,
    '.service_locator.dGUCsbe' => true,
    '.service_locator.dP7Bnj3' => true,
    '.service_locator.e5Iizj.' => true,
    '.service_locator.eI0wXYM' => true,
    '.service_locator.e_.xxAP' => true,
    '.service_locator.etVElvN' => true,
    '.service_locator.etVElvN.twig.template_cache_warmer' => true,
    '.service_locator.ewbgDlJ' => true,
    '.service_locator.fnHfynm' => true,
    '.service_locator.fuYM_Z.' => true,
    '.service_locator.fuYM_Z..translation.warmer' => true,
    '.service_locator.gFlme_s' => true,
    '.service_locator.gkYaBh_' => true,
    '.service_locator.h8Jkkd2' => true,
    '.service_locator.hOuXX4M' => true,
    '.service_locator.hnz5ZNh' => true,
    '.service_locator.hokdgZZ' => true,
    '.service_locator.jUv.zyj' => true,
    '.service_locator.jezs8TR' => true,
    '.service_locator.jqHm011' => true,
    '.service_locator.kleoBdA' => true,
    '.service_locator.lLv4pWF' => true,
    '.service_locator.lVEdkAN' => true,
    '.service_locator.mCbvPMW' => true,
    '.service_locator.mEW3YX9' => true,
    '.service_locator.mp9Jbac' => true,
    '.service_locator.oR77BOj' => true,
    '.service_locator.q81AI79' => true,
    '.service_locator.qDoK7Nx' => true,
    '.service_locator.qnvIrxy' => true,
    '.service_locator.rSTd.nA' => true,
    '.service_locator.ravAt47' => true,
    '.service_locator.ruxHxcA' => true,
    '.service_locator.s5axy1E' => true,
    '.service_locator.snsu9fy' => true,
    '.service_locator.uJMi0K7' => true,
    '.service_locator.uVvF4gL' => true,
    '.service_locator.ubULoiL' => true,
    '.service_locator.w7.f4fT' => true,
    '.service_locator.wOieFxO' => true,
    '.service_locator.x5IUIdO' => true,
    '.service_locator.xVsvWMS' => true,
    '.service_locator.xoBfZWU' => true,
    '.service_locator.xzYG5GW' => true,
    '.service_locator.y4_Zrx.' => true,
    '.service_locator.yY.p4P7' => true,
    '.twig.command.debug.lazy' => true,
    '.twig.command.lint.lazy' => true,
    '.var_dumper.command.server_dump.lazy' => true,
    'App\\Command\\CreateMasterAdminCommand' => true,
    'App\\Command\\CreateTestUsersAndReviewsCommand' => true,
    'App\\Command\\ImportCoursesCommand' => true,
    'App\\DataFixtures\\AppFixtures' => true,
    'App\\DataFixtures\\CourseFixtures' => true,
    'App\\DataFixtures\\InstructorFixtures' => true,
    'App\\DataFixtures\\MarketAnalysisFixtures' => true,
    'App\\Entity' => true,
    'App\\Form\\AdminFormType' => true,
    'App\\Form\\CategoryType' => true,
    'App\\Form\\ContactType' => true,
    'App\\Form\\CourseType' => true,
    'App\\Form\\InstructorType' => true,
    'App\\Form\\LoginFormType' => true,
    'App\\Form\\MarketAnalysisType' => true,
    'App\\Form\\PartnerType' => true,
    'App\\Form\\PlanType' => true,
    'App\\Form\\PromotionalBannerType' => true,
    'App\\Form\\RegistrationFormType' => true,
    'App\\Form\\VideoType' => true,
    'App\\Repository\\AdminRepository' => true,
    'App\\Repository\\CategoryRepository' => true,
    'App\\Repository\\CertificationRepository' => true,
    'App\\Repository\\ContactRepository' => true,
    'App\\Repository\\CountryRepository' => true,
    'App\\Repository\\CourseModuleRepository' => true,
    'App\\Repository\\CourseRepository' => true,
    'App\\Repository\\CourseReviewRepository' => true,
    'App\\Repository\\EnrollmentRepository' => true,
    'App\\Repository\\InstructorRepository' => true,
    'App\\Repository\\MarketAnalysisRepository' => true,
    'App\\Repository\\OrderRepository' => true,
    'App\\Repository\\PartnerRepository' => true,
    'App\\Repository\\PasswordResetTokenRepository' => true,
    'App\\Repository\\PaymentRepository' => true,
    'App\\Repository\\PlanRepository' => true,
    'App\\Repository\\PromotionalBannerRepository' => true,
    'App\\Repository\\UserRepository' => true,
    'App\\Repository\\UserVideoAccessRepository' => true,
    'App\\Repository\\VideoRepository' => true,
    'App\\Security\\LoginSuccessHandler' => true,
    'App\\Security\\UserChecker' => true,
    'App\\Service\\AccessControlService' => true,
    'App\\Service\\AdminNotificationService' => true,
    'App\\Service\\AdminPermissionService' => true,
    'App\\Service\\AssetService' => true,
    'App\\Service\\CartService' => true,
    'App\\Service\\CourseEnrollmentService' => true,
    'App\\Service\\EmailUniquenessValidator' => true,
    'App\\Service\\ErrorHandlingService' => true,
    'App\\Service\\FileUploadService' => true,
    'App\\Service\\IpAddressService' => true,
    'App\\Service\\MoodleService' => true,
    'App\\Service\\PasswordResetService' => true,
    'App\\Service\\PayPalService' => true,
    'App\\Service\\StripeService' => true,
    'App\\Service\\UnifiedContactService' => true,
    'App\\Service\\ValidationService' => true,
    'App\\Service\\VdoCipherService' => true,
    'App\\Twig\\AssetExtension' => true,
    'App\\Twig\\ContentExtension' => true,
    'App\\Twig\\PartnerExtension' => true,
    'App\\Twig\\PromotionalBannerExtension' => true,
    'Doctrine\\Bundle\\DoctrineBundle\\Dbal\\ManagerRegistryAwareConnectionProvider' => true,
    'Doctrine\\Common\\Persistence\\ManagerRegistry' => true,
    'Doctrine\\DBAL\\Connection' => true,
    'Doctrine\\DBAL\\Connection $defaultConnection' => true,
    'Doctrine\\DBAL\\Tools\\Console\\Command\\RunSqlCommand' => true,
    'Doctrine\\ORM\\EntityManagerInterface' => true,
    'Doctrine\\ORM\\EntityManagerInterface $defaultEntityManager' => true,
    'Doctrine\\Persistence\\ManagerRegistry' => true,
    'KnpU\\OAuth2ClientBundle\\Client\\ClientRegistry' => true,
    'KnpU\\OAuth2ClientBundle\\Client\\Provider\\GoogleClient' => true,
    'Psr\\Cache\\CacheItemPoolInterface' => true,
    'Psr\\Clock\\ClockInterface' => true,
    'Psr\\Container\\ContainerInterface $parameterBag' => true,
    'Psr\\EventDispatcher\\EventDispatcherInterface' => true,
    'Psr\\Http\\Client\\ClientInterface' => true,
    'Psr\\Log\\LoggerInterface' => true,
    'Psr\\Log\\LoggerInterface $assetMapperLogger' => true,
    'Psr\\Log\\LoggerInterface $cacheLogger' => true,
    'Psr\\Log\\LoggerInterface $consoleLogger' => true,
    'Psr\\Log\\LoggerInterface $debugLogger' => true,
    'Psr\\Log\\LoggerInterface $deprecationLogger' => true,
    'Psr\\Log\\LoggerInterface $doctrineLogger' => true,
    'Psr\\Log\\LoggerInterface $eventLogger' => true,
    'Psr\\Log\\LoggerInterface $httpClientLogger' => true,
    'Psr\\Log\\LoggerInterface $mailerLogger' => true,
    'Psr\\Log\\LoggerInterface $messengerLogger' => true,
    'Psr\\Log\\LoggerInterface $phpLogger' => true,
    'Psr\\Log\\LoggerInterface $profilerLogger' => true,
    'Psr\\Log\\LoggerInterface $requestLogger' => true,
    'Psr\\Log\\LoggerInterface $routerLogger' => true,
    'Psr\\Log\\LoggerInterface $securityLogger' => true,
    'Psr\\Log\\LoggerInterface $translationLogger' => true,
    'SessionHandlerInterface' => true,
    'Symfony\\Bundle\\SecurityBundle\\Security' => true,
    'Symfony\\Component\\AssetMapper\\AssetMapperInterface' => true,
    'Symfony\\Component\\AssetMapper\\ImportMap\\ImportMapManager' => true,
    'Symfony\\Component\\Asset\\Packages' => true,
    'Symfony\\Component\\Clock\\ClockInterface' => true,
    'Symfony\\Component\\Config\\Loader\\LoaderInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ReverseContainer' => true,
    'Symfony\\Component\\ErrorHandler\\ErrorRenderer\\FileLinkFormatter' => true,
    'Symfony\\Component\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Component\\Filesystem\\Filesystem' => true,
    'Symfony\\Component\\Form\\FormFactoryInterface' => true,
    'Symfony\\Component\\Form\\FormRegistryInterface' => true,
    'Symfony\\Component\\Form\\ResolvedFormTypeFactoryInterface' => true,
    'Symfony\\Component\\HttpFoundation\\Request' => true,
    'Symfony\\Component\\HttpFoundation\\RequestStack' => true,
    'Symfony\\Component\\HttpFoundation\\Response' => true,
    'Symfony\\Component\\HttpFoundation\\Session\\SessionInterface' => true,
    'Symfony\\Component\\HttpFoundation\\UriSigner' => true,
    'Symfony\\Component\\HttpFoundation\\UrlHelper' => true,
    'Symfony\\Component\\HttpKernel\\Config\\FileLocator' => true,
    'Symfony\\Component\\HttpKernel\\Fragment\\FragmentUriGeneratorInterface' => true,
    'Symfony\\Component\\HttpKernel\\HttpCache\\StoreInterface' => true,
    'Symfony\\Component\\HttpKernel\\HttpKernelInterface' => true,
    'Symfony\\Component\\HttpKernel\\KernelInterface' => true,
    'Symfony\\Component\\HttpKernel\\UriSigner' => true,
    'Symfony\\Component\\Mailer\\MailerInterface' => true,
    'Symfony\\Component\\Mailer\\Transport\\TransportInterface' => true,
    'Symfony\\Component\\Messenger\\MessageBusInterface' => true,
    'Symfony\\Component\\Messenger\\Transport\\Serialization\\SerializerInterface' => true,
    'Symfony\\Component\\Mime\\BodyRendererInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypeGuesserInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypesInterface' => true,
    'Symfony\\Component\\Notifier\\NotifierInterface' => true,
    'Symfony\\Component\\PasswordHasher\\Hasher\\PasswordHasherFactoryInterface' => true,
    'Symfony\\Component\\PasswordHasher\\Hasher\\UserPasswordHasherInterface' => true,
    'Symfony\\Component\\PropertyAccess\\PropertyAccessorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyAccessExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyDescriptionExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyInfoExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyInitializableExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyListExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyReadInfoExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyTypeExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyWriteInfoExtractorInterface' => true,
    'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface' => true,
    'Symfony\\Component\\Routing\\Matcher\\UrlMatcherInterface' => true,
    'Symfony\\Component\\Routing\\RequestContext' => true,
    'Symfony\\Component\\Routing\\RequestContextAwareInterface' => true,
    'Symfony\\Component\\Routing\\RouterInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authentication\\Token\\Storage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AccessDecisionManagerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AuthorizationCheckerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Role\\RoleHierarchyInterface' => true,
    'Symfony\\Component\\Security\\Core\\Security' => true,
    'Symfony\\Component\\Security\\Csrf\\CsrfTokenManagerInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenGenerator\\TokenGeneratorInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenStorage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Http\\Authentication\\AuthenticationUtils' => true,
    'Symfony\\Component\\Security\\Http\\Authentication\\UserAuthenticatorInterface' => true,
    'Symfony\\Component\\Security\\Http\\Firewall' => true,
    'Symfony\\Component\\Security\\Http\\FirewallMapInterface' => true,
    'Symfony\\Component\\Security\\Http\\HttpUtils' => true,
    'Symfony\\Component\\Security\\Http\\RememberMe\\RememberMeHandlerInterface' => true,
    'Symfony\\Component\\Security\\Http\\Session\\SessionAuthenticationStrategyInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\DecoderInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\EncoderInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\ClassDiscriminatorResolverInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\Factory\\ClassMetadataFactoryInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\DenormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\NormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\ObjectNormalizer' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\PropertyNormalizer' => true,
    'Symfony\\Component\\Serializer\\SerializerInterface' => true,
    'Symfony\\Component\\Stopwatch\\Stopwatch' => true,
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => true,
    'Symfony\\Component\\Translation\\Extractor\\ExtractorInterface' => true,
    'Symfony\\Component\\Translation\\LocaleSwitcher' => true,
    'Symfony\\Component\\Translation\\Reader\\TranslationReaderInterface' => true,
    'Symfony\\Component\\Translation\\Writer\\TranslationWriterInterface' => true,
    'Symfony\\Component\\Validator\\Constraints\\AllValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\AtLeastOneOfValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\BicValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\BlankValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CallbackValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CardSchemeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ChoiceValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CidrValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CollectionValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CompoundValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CountValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CountryValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CssColorValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CurrencyValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DateTimeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DateValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DivisibleByValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\EmailValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\EqualToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionLanguageSyntaxValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionSyntaxValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\FileValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\GreaterThanOrEqualValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\GreaterThanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\HostnameValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IbanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IdenticalToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ImageValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IpValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsFalseValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsNullValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsTrueValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsbnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsinValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IssnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\JsonValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LanguageValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LengthValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LessThanOrEqualValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LessThanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LocaleValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LuhnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NoSuspiciousCharactersValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotBlankValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotCompromisedPasswordValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotEqualToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotIdenticalToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotNullValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\PasswordStrengthValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\RangeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\RegexValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\SequentiallyValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TimeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TimezoneValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TypeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UlidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UniqueValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UrlValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UuidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ValidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\WhenValidator' => true,
    'Symfony\\Component\\Validator\\Validator\\ValidatorInterface' => true,
    'Symfony\\Component\\WebLink\\HttpHeaderSerializer' => true,
    'Symfony\\Contracts\\Cache\\CacheInterface' => true,
    'Symfony\\Contracts\\Cache\\TagAwareCacheInterface' => true,
    'Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Contracts\\HttpClient\\HttpClientInterface' => true,
    'Symfony\\Contracts\\Translation\\LocaleAwareInterface' => true,
    'Symfony\\Contracts\\Translation\\TranslatorInterface' => true,
    'Symfony\\UX\\Turbo\\Broadcaster\\BroadcasterInterface' => true,
    'Twig\\Environment' => true,
    'Twig_Environment' => true,
    'Vich\\UploaderBundle\\DataCollector\\MappingCollector' => true,
    'Vich\\UploaderBundle\\Form\\Type\\VichFileType' => true,
    'Vich\\UploaderBundle\\Form\\Type\\VichImageType' => true,
    'Vich\\UploaderBundle\\Handler\\DownloadHandler' => true,
    'Vich\\UploaderBundle\\Handler\\UploadHandler' => true,
    'Vich\\UploaderBundle\\Mapping\\PropertyMappingFactory' => true,
    'Vich\\UploaderBundle\\Mapping\\PropertyMappingResolverInterface' => true,
    'Vich\\UploaderBundle\\Metadata\\CacheWarmer' => true,
    'Vich\\UploaderBundle\\Storage\\FileSystemStorage' => true,
    'Vich\\UploaderBundle\\Storage\\StorageInterface' => true,
    'Vich\\UploaderBundle\\Templating\\Helper\\UploaderHelper' => true,
    'Vich\\UploaderBundle\\Twig\\Extension\\UploaderExtension' => true,
    'Vich\\UploaderBundle\\Twig\\Extension\\UploaderExtensionRuntime' => true,
    'Vich\\UploaderBundle\\Util\\Transliterator' => true,
    'argument_metadata_factory' => true,
    'argument_resolver' => true,
    'argument_resolver.backed_enum_resolver' => true,
    'argument_resolver.controller_locator' => true,
    'argument_resolver.datetime' => true,
    'argument_resolver.default' => true,
    'argument_resolver.not_tagged_controller' => true,
    'argument_resolver.query_parameter_value_resolver' => true,
    'argument_resolver.request' => true,
    'argument_resolver.request_attribute' => true,
    'argument_resolver.request_payload' => true,
    'argument_resolver.service' => true,
    'argument_resolver.session' => true,
    'argument_resolver.variadic' => true,
    'asset_mapper' => true,
    'asset_mapper.asset_package' => true,
    'asset_mapper.asset_package.inner' => true,
    'asset_mapper.cached_mapped_asset_factory' => true,
    'asset_mapper.cached_mapped_asset_factory.inner' => true,
    'asset_mapper.command.compile' => true,
    'asset_mapper.command.debug' => true,
    'asset_mapper.compiled_asset_mapper_config_reader' => true,
    'asset_mapper.compiler.css_asset_url_compiler' => true,
    'asset_mapper.compiler.javascript_import_path_compiler' => true,
    'asset_mapper.compiler.source_mapping_urls_compiler' => true,
    'asset_mapper.dev_server_subscriber' => true,
    'asset_mapper.http_client' => true,
    'asset_mapper.importmap.auditor' => true,
    'asset_mapper.importmap.command.audit' => true,
    'asset_mapper.importmap.command.install' => true,
    'asset_mapper.importmap.command.outdated' => true,
    'asset_mapper.importmap.command.remove' => true,
    'asset_mapper.importmap.command.require' => true,
    'asset_mapper.importmap.command.update' => true,
    'asset_mapper.importmap.config_reader' => true,
    'asset_mapper.importmap.generator' => true,
    'asset_mapper.importmap.manager' => true,
    'asset_mapper.importmap.remote_package_downloader' => true,
    'asset_mapper.importmap.remote_package_storage' => true,
    'asset_mapper.importmap.renderer' => true,
    'asset_mapper.importmap.resolver' => true,
    'asset_mapper.importmap.update_checker' => true,
    'asset_mapper.importmap.version_checker' => true,
    'asset_mapper.local_public_assets_filesystem' => true,
    'asset_mapper.mapped_asset_factory' => true,
    'asset_mapper.public_assets_path_resolver' => true,
    'asset_mapper.repository' => true,
    'asset_mapper_compiler' => true,
    'assets._default_package' => true,
    'assets.context' => true,
    'assets.empty_package' => true,
    'assets.empty_version_strategy' => true,
    'assets.json_manifest_version_strategy' => true,
    'assets.packages' => true,
    'assets.path_package' => true,
    'assets.static_version_strategy' => true,
    'assets.url_package' => true,
    'cache.adapter.apcu' => true,
    'cache.adapter.array' => true,
    'cache.adapter.doctrine_dbal' => true,
    'cache.adapter.filesystem' => true,
    'cache.adapter.memcached' => true,
    'cache.adapter.pdo' => true,
    'cache.adapter.psr6' => true,
    'cache.adapter.redis' => true,
    'cache.adapter.redis_tag_aware' => true,
    'cache.adapter.system' => true,
    'cache.annotations' => true,
    'cache.annotations.recorder_inner' => true,
    'cache.app.recorder_inner' => true,
    'cache.app.taggable' => true,
    'cache.asset_mapper' => true,
    'cache.asset_mapper.recorder_inner' => true,
    'cache.default_clearer' => true,
    'cache.default_doctrine_dbal_provider' => true,
    'cache.default_marshaller' => true,
    'cache.default_memcached_provider' => true,
    'cache.default_redis_provider' => true,
    'cache.doctrine.orm.default.metadata' => true,
    'cache.doctrine.orm.default.query' => true,
    'cache.doctrine.orm.default.query.recorder_inner' => true,
    'cache.doctrine.orm.default.result' => true,
    'cache.doctrine.orm.default.result.recorder_inner' => true,
    'cache.early_expiration_handler' => true,
    'cache.messenger.restart_workers_signal' => true,
    'cache.messenger.restart_workers_signal.recorder_inner' => true,
    'cache.property_access' => true,
    'cache.property_info' => true,
    'cache.property_info.recorder_inner' => true,
    'cache.security_expression_language' => true,
    'cache.security_expression_language.recorder_inner' => true,
    'cache.security_is_granted_attribute_expression_language.recorder_inner' => true,
    'cache.security_token_verifier' => true,
    'cache.security_token_verifier.recorder_inner' => true,
    'cache.serializer' => true,
    'cache.serializer.recorder_inner' => true,
    'cache.system.recorder_inner' => true,
    'cache.validator' => true,
    'cache.validator.recorder_inner' => true,
    'cache.validator_expression_language.recorder_inner' => true,
    'cache_clearer' => true,
    'cache_pool_clearer.cache_warmer' => true,
    'chatter.messenger.chat_handler' => true,
    'chatter.transport_factory' => true,
    'chatter.transports' => true,
    'clock' => true,
    'config.resource.self_checking_resource_checker' => true,
    'config_builder.warmer' => true,
    'config_cache_factory' => true,
    'console.command.about' => true,
    'console.command.assets_install' => true,
    'console.command.cache_clear' => true,
    'console.command.cache_pool_clear' => true,
    'console.command.cache_pool_delete' => true,
    'console.command.cache_pool_invalidate_tags' => true,
    'console.command.cache_pool_list' => true,
    'console.command.cache_pool_prune' => true,
    'console.command.cache_warmup' => true,
    'console.command.config_debug' => true,
    'console.command.config_dump_reference' => true,
    'console.command.container_debug' => true,
    'console.command.container_lint' => true,
    'console.command.debug_autowiring' => true,
    'console.command.dotenv_debug' => true,
    'console.command.event_dispatcher_debug' => true,
    'console.command.form_debug' => true,
    'console.command.mailer_test' => true,
    'console.command.messenger_consume_messages' => true,
    'console.command.messenger_debug' => true,
    'console.command.messenger_failed_messages_remove' => true,
    'console.command.messenger_failed_messages_retry' => true,
    'console.command.messenger_failed_messages_show' => true,
    'console.command.messenger_setup_transports' => true,
    'console.command.messenger_stats' => true,
    'console.command.messenger_stop_workers' => true,
    'console.command.router_debug' => true,
    'console.command.router_match' => true,
    'console.command.secrets_decrypt_to_local' => true,
    'console.command.secrets_encrypt_from_local' => true,
    'console.command.secrets_generate_key' => true,
    'console.command.secrets_list' => true,
    'console.command.secrets_remove' => true,
    'console.command.secrets_set' => true,
    'console.command.serializer_debug' => true,
    'console.command.translation_debug' => true,
    'console.command.translation_extract' => true,
    'console.command.translation_pull' => true,
    'console.command.translation_push' => true,
    'console.command.validator_debug' => true,
    'console.command.xliff_lint' => true,
    'console.command.yaml_lint' => true,
    'console.error_listener' => true,
    'console.messenger.application' => true,
    'console.messenger.execute_command_handler' => true,
    'console.suggest_missing_package_subscriber' => true,
    'console_profiler_listener' => true,
    'container.env' => true,
    'container.env_var_processor' => true,
    'container.getenv' => true,
    'controller.cache_attribute_listener' => true,
    'controller.is_granted_attribute_listener' => true,
    'controller.template_attribute_listener' => true,
    'controller_resolver' => true,
    'data_collector.ajax' => true,
    'data_collector.config' => true,
    'data_collector.doctrine' => true,
    'data_collector.events' => true,
    'data_collector.exception' => true,
    'data_collector.form' => true,
    'data_collector.form.extractor' => true,
    'data_collector.http_client' => true,
    'data_collector.logger' => true,
    'data_collector.memory' => true,
    'data_collector.messenger' => true,
    'data_collector.request' => true,
    'data_collector.request.session_collector' => true,
    'data_collector.router' => true,
    'data_collector.security' => true,
    'data_collector.time' => true,
    'data_collector.translation' => true,
    'data_collector.twig' => true,
    'data_collector.validator' => true,
    'debug.argument_resolver' => true,
    'debug.argument_resolver.inner' => true,
    'debug.controller_resolver' => true,
    'debug.controller_resolver.inner' => true,
    'debug.debug_handlers_listener' => true,
    'debug.debug_logger_configurator' => true,
    'debug.dump_listener' => true,
    'debug.event_dispatcher' => true,
    'debug.event_dispatcher.inner' => true,
    'debug.file_link_formatter' => true,
    'debug.file_link_formatter.url_format' => true,
    'debug.log_processor' => true,
    'debug.security.access.decision_manager' => true,
    'debug.security.access.decision_manager.inner' => true,
    'debug.security.event_dispatcher.admin' => true,
    'debug.security.event_dispatcher.admin.inner' => true,
    'debug.security.event_dispatcher.main' => true,
    'debug.security.event_dispatcher.main.inner' => true,
    'debug.security.firewall' => true,
    'debug.security.firewall.authenticator.admin' => true,
    'debug.security.firewall.authenticator.admin.inner' => true,
    'debug.security.firewall.authenticator.main' => true,
    'debug.security.firewall.authenticator.main.inner' => true,
    'debug.security.voter.vote_listener' => true,
    'debug.serializer' => true,
    'debug.serializer.inner' => true,
    'debug.traced.messenger.bus.default' => true,
    'debug.traced.messenger.bus.default.inner' => true,
    'debug.validator' => true,
    'debug.validator.inner' => true,
    'dependency_injection.config.container_parameters_resource_checker' => true,
    'disallow_search_engine_index_response_listener' => true,
    'doctrine.cache_clear_metadata_command' => true,
    'doctrine.cache_clear_query_cache_command' => true,
    'doctrine.cache_clear_result_command' => true,
    'doctrine.cache_collection_region_command' => true,
    'doctrine.clear_entity_region_command' => true,
    'doctrine.clear_query_region_command' => true,
    'doctrine.database_create_command' => true,
    'doctrine.database_drop_command' => true,
    'doctrine.dbal.connection' => true,
    'doctrine.dbal.connection.configuration' => true,
    'doctrine.dbal.connection.event_manager' => true,
    'doctrine.dbal.connection_expiries' => true,
    'doctrine.dbal.connection_factory' => true,
    'doctrine.dbal.connection_factory.dsn_parser' => true,
    'doctrine.dbal.debug_middleware' => true,
    'doctrine.dbal.debug_middleware.default' => true,
    'doctrine.dbal.default_connection.configuration' => true,
    'doctrine.dbal.default_connection.event_manager' => true,
    'doctrine.dbal.default_schema_asset_filter_manager' => true,
    'doctrine.dbal.default_schema_manager_factory' => true,
    'doctrine.dbal.event_manager' => true,
    'doctrine.dbal.legacy_schema_manager_factory' => true,
    'doctrine.dbal.logging_middleware' => true,
    'doctrine.dbal.logging_middleware.default' => true,
    'doctrine.dbal.schema_asset_filter_manager' => true,
    'doctrine.dbal.well_known_schema_asset_filter' => true,
    'doctrine.debug_data_holder' => true,
    'doctrine.fixtures.loader' => true,
    'doctrine.fixtures.purger.orm_purger_factory' => true,
    'doctrine.fixtures_load_command' => true,
    'doctrine.id_generator_locator' => true,
    'doctrine.mapping_info_command' => true,
    'doctrine.migrations.configuration' => true,
    'doctrine.migrations.configuration_loader' => true,
    'doctrine.migrations.connection_loader' => true,
    'doctrine.migrations.connection_registry_loader' => true,
    'doctrine.migrations.container_aware_migrations_factory' => true,
    'doctrine.migrations.container_aware_migrations_factory.inner' => true,
    'doctrine.migrations.dependency_factory' => true,
    'doctrine.migrations.em_loader' => true,
    'doctrine.migrations.entity_manager_registry_loader' => true,
    'doctrine.migrations.metadata_storage' => true,
    'doctrine.migrations.migrations_factory' => true,
    'doctrine.migrations.storage.table_storage' => true,
    'doctrine.orm.command.entity_manager_provider' => true,
    'doctrine.orm.configuration' => true,
    'doctrine.orm.container_repository_factory' => true,
    'doctrine.orm.default_attribute_metadata_driver' => true,
    'doctrine.orm.default_configuration' => true,
    'doctrine.orm.default_entity_listener_resolver' => true,
    'doctrine.orm.default_entity_manager.event_manager' => true,
    'doctrine.orm.default_entity_manager.property_info_extractor' => true,
    'doctrine.orm.default_entity_manager.validator_loader' => true,
    'doctrine.orm.default_listeners.attach_entity_listeners' => true,
    'doctrine.orm.default_manager_configurator' => true,
    'doctrine.orm.default_metadata_cache' => true,
    'doctrine.orm.default_metadata_driver' => true,
    'doctrine.orm.default_query_cache' => true,
    'doctrine.orm.default_result_cache' => true,
    'doctrine.orm.default_xml_metadata_driver' => true,
    'doctrine.orm.entity_manager.abstract' => true,
    'doctrine.orm.entity_value_resolver' => true,
    'doctrine.orm.entity_value_resolver.expression_language' => true,
    'doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener' => true,
    'doctrine.orm.listeners.doctrine_token_provider_schema_listener' => true,
    'doctrine.orm.listeners.lock_store_schema_listener' => true,
    'doctrine.orm.listeners.pdo_session_handler_schema_listener' => true,
    'doctrine.orm.listeners.resolve_target_entity' => true,
    'doctrine.orm.manager_configurator.abstract' => true,
    'doctrine.orm.messenger.doctrine_schema_listener' => true,
    'doctrine.orm.messenger.event_subscriber.doctrine_clear_entity_manager' => true,
    'doctrine.orm.naming_strategy.default' => true,
    'doctrine.orm.naming_strategy.underscore' => true,
    'doctrine.orm.naming_strategy.underscore_number_aware' => true,
    'doctrine.orm.proxy_cache_warmer' => true,
    'doctrine.orm.quote_strategy.ansi' => true,
    'doctrine.orm.quote_strategy.default' => true,
    'doctrine.orm.security.user.provider' => true,
    'doctrine.orm.typed_field_mapper.default' => true,
    'doctrine.orm.validator.unique' => true,
    'doctrine.orm.validator_initializer' => true,
    'doctrine.query_dql_command' => true,
    'doctrine.query_sql_command' => true,
    'doctrine.schema_create_command' => true,
    'doctrine.schema_drop_command' => true,
    'doctrine.schema_update_command' => true,
    'doctrine.schema_validate_command' => true,
    'doctrine.twig.doctrine_extension' => true,
    'doctrine.ulid_generator' => true,
    'doctrine.uuid_generator' => true,
    'doctrine_migrations.current_command' => true,
    'doctrine_migrations.diff_command' => true,
    'doctrine_migrations.dump_schema_command' => true,
    'doctrine_migrations.execute_command' => true,
    'doctrine_migrations.generate_command' => true,
    'doctrine_migrations.latest_command' => true,
    'doctrine_migrations.migrate_command' => true,
    'doctrine_migrations.rollup_command' => true,
    'doctrine_migrations.schema_filter_listener' => true,
    'doctrine_migrations.status_command' => true,
    'doctrine_migrations.sync_metadata_command' => true,
    'doctrine_migrations.up_to_date_command' => true,
    'doctrine_migrations.version_command' => true,
    'doctrine_migrations.versions_command' => true,
    'error_handler.error_renderer.html' => true,
    'error_handler.error_renderer.serializer' => true,
    'error_renderer' => true,
    'error_renderer.html' => true,
    'error_renderer.serializer' => true,
    'exception_listener' => true,
    'file_locator' => true,
    'filesystem' => true,
    'form.choice_list_factory' => true,
    'form.choice_list_factory.cached' => true,
    'form.choice_list_factory.default' => true,
    'form.choice_list_factory.property_access' => true,
    'form.extension' => true,
    'form.factory' => true,
    'form.listener.password_hasher' => true,
    'form.property_accessor' => true,
    'form.registry' => true,
    'form.resolved_type_factory' => true,
    'form.server_params' => true,
    'form.type.choice' => true,
    'form.type.color' => true,
    'form.type.entity' => true,
    'form.type.file' => true,
    'form.type.form' => true,
    'form.type_extension.csrf' => true,
    'form.type_extension.form.data_collector' => true,
    'form.type_extension.form.http_foundation' => true,
    'form.type_extension.form.password_hasher' => true,
    'form.type_extension.form.request_handler' => true,
    'form.type_extension.form.transformation_failure_handling' => true,
    'form.type_extension.form.validator' => true,
    'form.type_extension.password.password_hasher' => true,
    'form.type_extension.repeated.validator' => true,
    'form.type_extension.submit.validator' => true,
    'form.type_extension.upload.validator' => true,
    'form.type_guesser.doctrine' => true,
    'form.type_guesser.validator' => true,
    'fragment.handler' => true,
    'fragment.renderer.inline' => true,
    'fragment.uri_generator' => true,
    'http_cache' => true,
    'http_cache.store' => true,
    'http_client' => true,
    'http_client.abstract_retry_strategy' => true,
    'http_client.messenger.ping_webhook_handler' => true,
    'http_client.transport' => true,
    'http_client.uri_template' => true,
    'http_client.uri_template.inner' => true,
    'http_client.uri_template_expander.guzzle' => true,
    'http_client.uri_template_expander.rize' => true,
    'identity_translator' => true,
    'knpu.oauth2.provider.google' => true,
    'knpu.oauth2.provider_factory' => true,
    'knpu.oauth2.user_provider' => true,
    'locale_aware_listener' => true,
    'locale_listener' => true,
    'logger' => true,
    'mailer' => true,
    'mailer.data_collector' => true,
    'mailer.default_transport' => true,
    'mailer.envelope_listener' => true,
    'mailer.mailer' => true,
    'mailer.message_logger_listener' => true,
    'mailer.messenger.message_handler' => true,
    'mailer.messenger_transport_listener' => true,
    'mailer.transport_factory' => true,
    'mailer.transport_factory.abstract' => true,
    'mailer.transport_factory.native' => true,
    'mailer.transport_factory.null' => true,
    'mailer.transport_factory.sendmail' => true,
    'mailer.transport_factory.smtp' => true,
    'mailer.transports' => true,
    'maker.auto_command.abstract' => true,
    'maker.auto_command.make_auth' => true,
    'maker.auto_command.make_command' => true,
    'maker.auto_command.make_controller' => true,
    'maker.auto_command.make_crud' => true,
    'maker.auto_command.make_docker_database' => true,
    'maker.auto_command.make_entity' => true,
    'maker.auto_command.make_fixtures' => true,
    'maker.auto_command.make_form' => true,
    'maker.auto_command.make_listener' => true,
    'maker.auto_command.make_message' => true,
    'maker.auto_command.make_messenger_middleware' => true,
    'maker.auto_command.make_migration' => true,
    'maker.auto_command.make_registration_form' => true,
    'maker.auto_command.make_reset_password' => true,
    'maker.auto_command.make_schedule' => true,
    'maker.auto_command.make_security_custom' => true,
    'maker.auto_command.make_security_form_login' => true,
    'maker.auto_command.make_serializer_encoder' => true,
    'maker.auto_command.make_serializer_normalizer' => true,
    'maker.auto_command.make_stimulus_controller' => true,
    'maker.auto_command.make_test' => true,
    'maker.auto_command.make_twig_component' => true,
    'maker.auto_command.make_twig_extension' => true,
    'maker.auto_command.make_user' => true,
    'maker.auto_command.make_validator' => true,
    'maker.auto_command.make_voter' => true,
    'maker.auto_command.make_webhook' => true,
    'maker.autoloader_finder' => true,
    'maker.autoloader_util' => true,
    'maker.console_error_listener' => true,
    'maker.doctrine_helper' => true,
    'maker.entity_class_generator' => true,
    'maker.event_registry' => true,
    'maker.file_link_formatter' => true,
    'maker.file_manager' => true,
    'maker.generator' => true,
    'maker.maker.make_authenticator' => true,
    'maker.maker.make_command' => true,
    'maker.maker.make_controller' => true,
    'maker.maker.make_crud' => true,
    'maker.maker.make_custom_authenticator' => true,
    'maker.maker.make_docker_database' => true,
    'maker.maker.make_entity' => true,
    'maker.maker.make_fixtures' => true,
    'maker.maker.make_form' => true,
    'maker.maker.make_form_login' => true,
    'maker.maker.make_functional_test' => true,
    'maker.maker.make_listener' => true,
    'maker.maker.make_message' => true,
    'maker.maker.make_messenger_middleware' => true,
    'maker.maker.make_migration' => true,
    'maker.maker.make_registration_form' => true,
    'maker.maker.make_reset_password' => true,
    'maker.maker.make_schedule' => true,
    'maker.maker.make_serializer_encoder' => true,
    'maker.maker.make_serializer_normalizer' => true,
    'maker.maker.make_stimulus_controller' => true,
    'maker.maker.make_subscriber' => true,
    'maker.maker.make_test' => true,
    'maker.maker.make_twig_component' => true,
    'maker.maker.make_twig_extension' => true,
    'maker.maker.make_unit_test' => true,
    'maker.maker.make_user' => true,
    'maker.maker.make_validator' => true,
    'maker.maker.make_voter' => true,
    'maker.maker.make_webhook' => true,
    'maker.php_compat_util' => true,
    'maker.renderer.form_type_renderer' => true,
    'maker.security_config_updater' => true,
    'maker.security_controller_builder' => true,
    'maker.template_component_generator' => true,
    'maker.template_linter' => true,
    'maker.user_class_builder' => true,
    'messenger.bus.default' => true,
    'messenger.bus.default.messenger.handlers_locator' => true,
    'messenger.bus.default.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.bus.default.middleware.handle_message' => true,
    'messenger.bus.default.middleware.send_message' => true,
    'messenger.bus.default.middleware.traceable' => true,
    'messenger.default_serializer' => true,
    'messenger.failure.add_error_details_stamp_listener' => true,
    'messenger.failure.send_failed_message_to_failure_transport_listener' => true,
    'messenger.failure_transports.default' => true,
    'messenger.listener.dispatch_pcntl_signal_listener' => true,
    'messenger.listener.reset_services' => true,
    'messenger.listener.stop_worker_on_restart_signal_listener' => true,
    'messenger.listener.stop_worker_on_sigterm_signal_listener' => true,
    'messenger.listener.stop_worker_on_stop_exception_listener' => true,
    'messenger.listener.stop_worker_signals_listener' => true,
    'messenger.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.middleware.dispatch_after_current_bus' => true,
    'messenger.middleware.doctrine_close_connection' => true,
    'messenger.middleware.doctrine_open_transaction_logger' => true,
    'messenger.middleware.doctrine_ping_connection' => true,
    'messenger.middleware.doctrine_transaction' => true,
    'messenger.middleware.failed_message_processing_middleware' => true,
    'messenger.middleware.handle_message' => true,
    'messenger.middleware.reject_redelivered_message_middleware' => true,
    'messenger.middleware.router_context' => true,
    'messenger.middleware.send_message' => true,
    'messenger.middleware.traceable' => true,
    'messenger.middleware.validation' => true,
    'messenger.receiver_locator' => true,
    'messenger.redispatch_message_handler' => true,
    'messenger.retry.abstract_multiplier_retry_strategy' => true,
    'messenger.retry.multiplier_retry_strategy.async' => true,
    'messenger.retry.multiplier_retry_strategy.failed' => true,
    'messenger.retry.send_failed_message_for_retry_listener' => true,
    'messenger.retry_strategy_locator' => true,
    'messenger.routable_message_bus' => true,
    'messenger.senders_locator' => true,
    'messenger.transport.amqp.factory' => true,
    'messenger.transport.async' => true,
    'messenger.transport.beanstalkd.factory' => true,
    'messenger.transport.doctrine.factory' => true,
    'messenger.transport.failed' => true,
    'messenger.transport.in_memory.factory' => true,
    'messenger.transport.native_php_serializer' => true,
    'messenger.transport.redis.factory' => true,
    'messenger.transport.sqs.factory' => true,
    'messenger.transport.symfony_serializer' => true,
    'messenger.transport.sync.factory' => true,
    'messenger.transport_factory' => true,
    'mime_types' => true,
    'monolog.activation_strategy.not_found' => true,
    'monolog.command.server_log' => true,
    'monolog.formatter.chrome_php' => true,
    'monolog.formatter.gelf_message' => true,
    'monolog.formatter.html' => true,
    'monolog.formatter.json' => true,
    'monolog.formatter.line' => true,
    'monolog.formatter.loggly' => true,
    'monolog.formatter.logstash' => true,
    'monolog.formatter.normalizer' => true,
    'monolog.formatter.scalar' => true,
    'monolog.formatter.wildfire' => true,
    'monolog.handler.console' => true,
    'monolog.handler.fingers_crossed.error_level_activation_strategy' => true,
    'monolog.handler.main' => true,
    'monolog.handler.null_internal' => true,
    'monolog.http_client' => true,
    'monolog.logger' => true,
    'monolog.logger.asset_mapper' => true,
    'monolog.logger.cache' => true,
    'monolog.logger.console' => true,
    'monolog.logger.debug' => true,
    'monolog.logger.doctrine' => true,
    'monolog.logger.event' => true,
    'monolog.logger.http_client' => true,
    'monolog.logger.mailer' => true,
    'monolog.logger.messenger' => true,
    'monolog.logger.php' => true,
    'monolog.logger.profiler' => true,
    'monolog.logger.request' => true,
    'monolog.logger.router' => true,
    'monolog.logger.security' => true,
    'monolog.logger.translation' => true,
    'monolog.logger_prototype' => true,
    'monolog.processor.psr_log_message' => true,
    'notifier' => true,
    'notifier.admin_recipient.0' => true,
    'notifier.channel.browser' => true,
    'notifier.channel.chat' => true,
    'notifier.channel.email' => true,
    'notifier.channel.push' => true,
    'notifier.channel.sms' => true,
    'notifier.channel_policy' => true,
    'notifier.data_collector' => true,
    'notifier.failed_message_listener' => true,
    'notifier.flash_message_importance_mapper' => true,
    'notifier.logger_notification_listener' => true,
    'notifier.monolog_handler' => true,
    'notifier.notification_logger_listener' => true,
    'notifier.transport_factory.abstract' => true,
    'notifier.transport_factory.null' => true,
    'parameter_bag' => true,
    'process.messenger.process_message_handler' => true,
    'profiler.storage' => true,
    'profiler_listener' => true,
    'property_accessor' => true,
    'property_info' => true,
    'property_info.php_doc_extractor' => true,
    'property_info.phpstan_extractor' => true,
    'property_info.reflection_extractor' => true,
    'property_info.serializer_extractor' => true,
    'psr18.http_client' => true,
    'response_listener' => true,
    'reverse_container' => true,
    'router.cache_warmer' => true,
    'router.default' => true,
    'router.expression_language_provider' => true,
    'router.request_context' => true,
    'router_listener' => true,
    'routing.loader.annotation' => true,
    'routing.loader.annotation.directory' => true,
    'routing.loader.annotation.file' => true,
    'routing.loader.attribute' => true,
    'routing.loader.attribute.directory' => true,
    'routing.loader.attribute.file' => true,
    'routing.loader.container' => true,
    'routing.loader.directory' => true,
    'routing.loader.glob' => true,
    'routing.loader.php' => true,
    'routing.loader.psr4' => true,
    'routing.loader.xml' => true,
    'routing.loader.yml' => true,
    'routing.resolver' => true,
    'secrets.decryption_key' => true,
    'secrets.local_vault' => true,
    'secrets.vault' => true,
    'security.access.authenticated_voter' => true,
    'security.access.decision_manager' => true,
    'security.access.expression_voter' => true,
    'security.access.simple_role_voter' => true,
    'security.access_listener' => true,
    'security.access_map' => true,
    'security.access_token_extractor.header' => true,
    'security.access_token_extractor.query_string' => true,
    'security.access_token_extractor.request_body' => true,
    'security.access_token_handler.oidc' => true,
    'security.access_token_handler.oidc.jwk' => true,
    'security.access_token_handler.oidc.signature' => true,
    'security.access_token_handler.oidc.signature.ES256' => true,
    'security.access_token_handler.oidc.signature.ES384' => true,
    'security.access_token_handler.oidc.signature.ES512' => true,
    'security.access_token_handler.oidc_user_info' => true,
    'security.access_token_handler.oidc_user_info.http_client' => true,
    'security.authentication.custom_failure_handler' => true,
    'security.authentication.custom_success_handler' => true,
    'security.authentication.failure_handler' => true,
    'security.authentication.failure_handler.admin.form_login' => true,
    'security.authentication.failure_handler.main.form_login' => true,
    'security.authentication.listener.abstract' => true,
    'security.authentication.session_strategy' => true,
    'security.authentication.session_strategy.admin' => true,
    'security.authentication.session_strategy.main' => true,
    'security.authentication.session_strategy_noop' => true,
    'security.authentication.success_handler' => true,
    'security.authentication.success_handler.admin.form_login' => true,
    'security.authentication.success_handler.main.form_login' => true,
    'security.authentication.switchuser_listener' => true,
    'security.authentication.trust_resolver' => true,
    'security.authentication_utils' => true,
    'security.authenticator.access_token' => true,
    'security.authenticator.access_token.chain_extractor' => true,
    'security.authenticator.firewall_aware_remember_me_handler' => true,
    'security.authenticator.form_login' => true,
    'security.authenticator.form_login.admin' => true,
    'security.authenticator.form_login.main' => true,
    'security.authenticator.http_basic' => true,
    'security.authenticator.json_login' => true,
    'security.authenticator.manager' => true,
    'security.authenticator.manager.admin' => true,
    'security.authenticator.manager.main' => true,
    'security.authenticator.managers_locator' => true,
    'security.authenticator.persistent_remember_me_handler' => true,
    'security.authenticator.remember_me' => true,
    'security.authenticator.remember_me.main' => true,
    'security.authenticator.remember_me_handler.main' => true,
    'security.authenticator.remember_me_signature_hasher' => true,
    'security.authenticator.remember_me_signature_hasher.main' => true,
    'security.authenticator.remote_user' => true,
    'security.authenticator.signature_remember_me_handler' => true,
    'security.authenticator.x509' => true,
    'security.authorization_checker' => true,
    'security.channel_listener' => true,
    'security.command.debug_firewall' => true,
    'security.command.user_password_hash' => true,
    'security.context_listener' => true,
    'security.context_listener.0' => true,
    'security.context_listener.1' => true,
    'security.csrf.token_generator' => true,
    'security.csrf.token_manager' => true,
    'security.csrf.token_storage' => true,
    'security.event_dispatcher.admin' => true,
    'security.event_dispatcher.main' => true,
    'security.exception_listener' => true,
    'security.exception_listener.admin' => true,
    'security.exception_listener.main' => true,
    'security.expression_language' => true,
    'security.firewall' => true,
    'security.firewall.authenticator' => true,
    'security.firewall.authenticator.admin' => true,
    'security.firewall.authenticator.main' => true,
    'security.firewall.config' => true,
    'security.firewall.context' => true,
    'security.firewall.context_locator' => true,
    'security.firewall.event_dispatcher_locator' => true,
    'security.firewall.lazy_context' => true,
    'security.firewall.map' => true,
    'security.firewall.map.config.admin' => true,
    'security.firewall.map.config.dev' => true,
    'security.firewall.map.config.main' => true,
    'security.firewall.map.context.admin' => true,
    'security.firewall.map.context.dev' => true,
    'security.firewall.map.context.main' => true,
    'security.helper' => true,
    'security.http_utils' => true,
    'security.impersonate_url_generator' => true,
    'security.is_granted_attribute_expression_language' => true,
    'security.ldap_locator' => true,
    'security.listener.admin.user_provider' => true,
    'security.listener.check_authenticator_credentials' => true,
    'security.listener.check_remember_me_conditions' => true,
    'security.listener.check_remember_me_conditions.main' => true,
    'security.listener.csrf_protection' => true,
    'security.listener.login_throttling' => true,
    'security.listener.main.user_provider' => true,
    'security.listener.password_migrating' => true,
    'security.listener.remember_me' => true,
    'security.listener.remember_me.main' => true,
    'security.listener.session' => true,
    'security.listener.session.admin' => true,
    'security.listener.session.main' => true,
    'security.listener.user_checker' => true,
    'security.listener.user_checker.admin' => true,
    'security.listener.user_checker.main' => true,
    'security.listener.user_provider' => true,
    'security.listener.user_provider.abstract' => true,
    'security.logout.listener.clear_site_data' => true,
    'security.logout.listener.cookie_clearing' => true,
    'security.logout.listener.cookie_clearing.admin' => true,
    'security.logout.listener.cookie_clearing.main' => true,
    'security.logout.listener.csrf_token_clearing' => true,
    'security.logout.listener.default' => true,
    'security.logout.listener.default.admin' => true,
    'security.logout.listener.default.main' => true,
    'security.logout.listener.session' => true,
    'security.logout.listener.session.admin' => true,
    'security.logout.listener.session.main' => true,
    'security.logout_listener' => true,
    'security.logout_listener.admin' => true,
    'security.logout_listener.main' => true,
    'security.logout_url_generator' => true,
    'security.password_hasher' => true,
    'security.password_hasher_factory' => true,
    'security.rememberme.response_listener' => true,
    'security.role_hierarchy' => true,
    'security.route_loader.logout' => true,
    'security.security_token_value_resolver' => true,
    'security.token_storage' => true,
    'security.untracked_token_storage' => true,
    'security.user.provider.chain' => true,
    'security.user.provider.concrete.app_admin_provider' => true,
    'security.user.provider.concrete.app_unified_provider' => true,
    'security.user.provider.concrete.app_user_provider' => true,
    'security.user.provider.in_memory' => true,
    'security.user.provider.ldap' => true,
    'security.user.provider.missing' => true,
    'security.user_authenticator' => true,
    'security.user_checker' => true,
    'security.user_checker.admin' => true,
    'security.user_checker.chain.admin' => true,
    'security.user_checker.chain.main' => true,
    'security.user_checker.main' => true,
    'security.user_checker_locator' => true,
    'security.user_password_hasher' => true,
    'security.user_providers' => true,
    'security.user_value_resolver' => true,
    'security.validator.user_password' => true,
    'serializer' => true,
    'serializer.data_collector' => true,
    'serializer.denormalizer.array' => true,
    'serializer.denormalizer.unwrapping' => true,
    'serializer.encoder.csv' => true,
    'serializer.encoder.json' => true,
    'serializer.encoder.xml' => true,
    'serializer.encoder.yaml' => true,
    'serializer.mapping.cache.symfony' => true,
    'serializer.mapping.cache_warmer' => true,
    'serializer.mapping.chain_loader' => true,
    'serializer.mapping.class_discriminator_resolver' => true,
    'serializer.mapping.class_metadata_factory' => true,
    'serializer.name_converter.camel_case_to_snake_case' => true,
    'serializer.name_converter.metadata_aware' => true,
    'serializer.normalizer.backed_enum' => true,
    'serializer.normalizer.constraint_violation_list' => true,
    'serializer.normalizer.data_uri' => true,
    'serializer.normalizer.dateinterval' => true,
    'serializer.normalizer.datetime' => true,
    'serializer.normalizer.datetimezone' => true,
    'serializer.normalizer.flatten_exception' => true,
    'serializer.normalizer.form_error' => true,
    'serializer.normalizer.json_serializable' => true,
    'serializer.normalizer.mime_message' => true,
    'serializer.normalizer.object' => true,
    'serializer.normalizer.problem' => true,
    'serializer.normalizer.property' => true,
    'serializer.normalizer.translatable' => true,
    'serializer.normalizer.uid' => true,
    'serializer.property_accessor' => true,
    'session.abstract_handler' => true,
    'session.factory' => true,
    'session.handler' => true,
    'session.handler.native' => true,
    'session.handler.native_file' => true,
    'session.marshaller' => true,
    'session.marshalling_handler' => true,
    'session.storage.factory' => true,
    'session.storage.factory.mock_file' => true,
    'session.storage.factory.native' => true,
    'session.storage.factory.php_bridge' => true,
    'session_listener' => true,
    'slugger' => true,
    'stimulus.asset_mapper.auto_import_locator' => true,
    'stimulus.asset_mapper.controllers_map_generator' => true,
    'stimulus.asset_mapper.loader_javascript_compiler' => true,
    'stimulus.asset_mapper.ux_package_reader' => true,
    'stimulus.helper' => true,
    'stimulus.twig_extension' => true,
    'stimulus.ux_controllers_twig_extension' => true,
    'stimulus.ux_controllers_twig_runtime' => true,
    'texter.messenger.push_handler' => true,
    'texter.messenger.sms_handler' => true,
    'texter.transport_factory' => true,
    'texter.transports' => true,
    'translation.dumper.csv' => true,
    'translation.dumper.ini' => true,
    'translation.dumper.json' => true,
    'translation.dumper.mo' => true,
    'translation.dumper.php' => true,
    'translation.dumper.po' => true,
    'translation.dumper.qt' => true,
    'translation.dumper.res' => true,
    'translation.dumper.xliff' => true,
    'translation.dumper.xliff.xliff' => true,
    'translation.dumper.yaml' => true,
    'translation.dumper.yml' => true,
    'translation.extractor' => true,
    'translation.extractor.php_ast' => true,
    'translation.extractor.visitor.constraint' => true,
    'translation.extractor.visitor.trans_method' => true,
    'translation.extractor.visitor.translatable_message' => true,
    'translation.loader.csv' => true,
    'translation.loader.dat' => true,
    'translation.loader.ini' => true,
    'translation.loader.json' => true,
    'translation.loader.mo' => true,
    'translation.loader.php' => true,
    'translation.loader.po' => true,
    'translation.loader.qt' => true,
    'translation.loader.res' => true,
    'translation.loader.xliff' => true,
    'translation.loader.yml' => true,
    'translation.locale_switcher' => true,
    'translation.provider_collection' => true,
    'translation.provider_collection_factory' => true,
    'translation.provider_factory.null' => true,
    'translation.reader' => true,
    'translation.warmer' => true,
    'translation.writer' => true,
    'translator.data_collector' => true,
    'translator.data_collector.inner' => true,
    'translator.default' => true,
    'translator.formatter' => true,
    'translator.formatter.default' => true,
    'translator.logging' => true,
    'turbo.broadcaster.action_renderer' => true,
    'turbo.broadcaster.action_renderer.inner' => true,
    'turbo.broadcaster.imux' => true,
    'turbo.doctrine.event_listener' => true,
    'turbo.id_accessor' => true,
    'turbo.kernel.request_listener' => true,
    'turbo.twig.extension' => true,
    'turbo.twig.runtime' => true,
    'twig' => true,
    'twig.app_variable' => true,
    'twig.command.debug' => true,
    'twig.command.lint' => true,
    'twig.configurator.environment' => true,
    'twig.error_renderer.html' => true,
    'twig.error_renderer.html.inner' => true,
    'twig.extension.assets' => true,
    'twig.extension.code' => true,
    'twig.extension.debug' => true,
    'twig.extension.debug.stopwatch' => true,
    'twig.extension.dump' => true,
    'twig.extension.expression' => true,
    'twig.extension.form' => true,
    'twig.extension.htmlsanitizer' => true,
    'twig.extension.httpfoundation' => true,
    'twig.extension.httpkernel' => true,
    'twig.extension.importmap' => true,
    'twig.extension.logout_url' => true,
    'twig.extension.profiler' => true,
    'twig.extension.routing' => true,
    'twig.extension.security' => true,
    'twig.extension.security_csrf' => true,
    'twig.extension.serializer' => true,
    'twig.extension.trans' => true,
    'twig.extension.weblink' => true,
    'twig.extension.webprofiler' => true,
    'twig.extension.yaml' => true,
    'twig.form.engine' => true,
    'twig.form.renderer' => true,
    'twig.loader' => true,
    'twig.loader.chain' => true,
    'twig.loader.filesystem' => true,
    'twig.loader.native_filesystem' => true,
    'twig.mailer.message_listener' => true,
    'twig.mime_body_renderer' => true,
    'twig.missing_extension_suggestor' => true,
    'twig.profile' => true,
    'twig.runtime.httpkernel' => true,
    'twig.runtime.importmap' => true,
    'twig.runtime.security_csrf' => true,
    'twig.runtime.serializer' => true,
    'twig.runtime_loader' => true,
    'twig.template_cache_warmer' => true,
    'twig.template_iterator' => true,
    'twig.translation.extractor' => true,
    'uri_signer' => true,
    'url_helper' => true,
    'validate_request_listener' => true,
    'validator' => true,
    'validator.builder' => true,
    'validator.email' => true,
    'validator.expression' => true,
    'validator.expression_language' => true,
    'validator.expression_language_provider' => true,
    'validator.mapping.cache.adapter' => true,
    'validator.mapping.cache_warmer' => true,
    'validator.mapping.class_metadata_factory' => true,
    'validator.no_suspicious_characters' => true,
    'validator.not_compromised_password' => true,
    'validator.property_info_loader' => true,
    'validator.validator_factory' => true,
    'validator.when' => true,
    'var_dumper.cli_dumper' => true,
    'var_dumper.command.server_dump' => true,
    'var_dumper.contextualized_cli_dumper' => true,
    'var_dumper.contextualized_cli_dumper.inner' => true,
    'var_dumper.dump_server' => true,
    'var_dumper.html_dumper' => true,
    'var_dumper.server_connection' => true,
    'vich_uploader.adapter.mongodb' => true,
    'vich_uploader.adapter.orm' => true,
    'vich_uploader.adapter.phpcr' => true,
    'vich_uploader.command.mapping_debug' => true,
    'vich_uploader.command.mapping_debug_class' => true,
    'vich_uploader.command.mapping_list_classes' => true,
    'vich_uploader.file_injector' => true,
    'vich_uploader.listener.clean.admin_profiles' => true,
    'vich_uploader.listener.clean.homepage_sections' => true,
    'vich_uploader.listener.clean.instructor_profiles' => true,
    'vich_uploader.listener.clean.mongodb' => true,
    'vich_uploader.listener.clean.orm' => true,
    'vich_uploader.listener.clean.phpcr' => true,
    'vich_uploader.listener.doctrine.base' => true,
    'vich_uploader.listener.inject.mongodb' => true,
    'vich_uploader.listener.inject.orm' => true,
    'vich_uploader.listener.inject.phpcr' => true,
    'vich_uploader.listener.remove.admin_profiles' => true,
    'vich_uploader.listener.remove.homepage_sections' => true,
    'vich_uploader.listener.remove.instructor_profiles' => true,
    'vich_uploader.listener.remove.mongodb' => true,
    'vich_uploader.listener.remove.orm' => true,
    'vich_uploader.listener.remove.phpcr' => true,
    'vich_uploader.listener.upload.admin_profiles' => true,
    'vich_uploader.listener.upload.homepage_sections' => true,
    'vich_uploader.listener.upload.instructor_profiles' => true,
    'vich_uploader.listener.upload.mongodb' => true,
    'vich_uploader.listener.upload.orm' => true,
    'vich_uploader.listener.upload.phpcr' => true,
    'vich_uploader.metadata.attribute_reader' => true,
    'vich_uploader.metadata.cache' => true,
    'vich_uploader.metadata.cache.file_cache' => true,
    'vich_uploader.metadata.file_locator' => true,
    'vich_uploader.metadata.reader' => true,
    'vich_uploader.metadata_driver' => true,
    'vich_uploader.metadata_driver.annotation' => true,
    'vich_uploader.metadata_driver.chain' => true,
    'vich_uploader.metadata_driver.xml' => true,
    'vich_uploader.metadata_driver.yaml' => true,
    'vich_uploader.metadata_driver.yml' => true,
    'vich_uploader.metadata_factory' => true,
    'vich_uploader.metadata_reader' => true,
    'vich_uploader.property_mapping_factory' => true,
    'vich_uploader.property_mapping_resolver' => true,
    'vich_uploader.storage' => true,
    'vich_uploader.storage.file_system' => true,
    'web_link.add_link_header_listener' => true,
    'web_link.http_header_serializer' => true,
    'web_profiler.csp.handler' => true,
    'web_profiler.debug_toolbar' => true,
    'workflow.twig_extension' => true,
];

<?php

namespace ContainerR8yDayf;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCreateTestUsersAndReviewsCommand_LazyService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.App\Command\CreateTestUsersAndReviewsCommand.lazy' shared service.
     *
     * @return \Symfony\Component\Console\Command\LazyCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'LazyCommand.php';

        return $container->privates['.App\\Command\\CreateTestUsersAndReviewsCommand.lazy'] = new \Symfony\Component\Console\Command\LazyCommand('app:create-test-users-reviews', [], 'Create 3 test users and add certified reviews for CS101 course', false, #[\Closure(name: 'App\\Command\\CreateTestUsersAndReviewsCommand')] fn (): \App\Command\CreateTestUsersAndReviewsCommand => ($container->privates['App\\Command\\CreateTestUsersAndReviewsCommand'] ?? $container->load('getCreateTestUsersAndReviewsCommandService')));
    }
}

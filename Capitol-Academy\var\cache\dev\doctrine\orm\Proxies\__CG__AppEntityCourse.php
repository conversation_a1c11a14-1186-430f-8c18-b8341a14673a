<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Course extends \App\Entity\Course implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'active_enrollments' => [parent::class, 'active_enrollments', null, 16],
        "\0".parent::class."\0".'average_rating' => [parent::class, 'average_rating', null, 16],
        "\0".parent::class."\0".'banner_image' => [parent::class, 'banner_image', null, 16],
        "\0".parent::class."\0".'category' => [parent::class, 'category', null, 16],
        "\0".parent::class."\0".'certified_count' => [parent::class, 'certified_count', null, 16],
        "\0".parent::class."\0".'code' => [parent::class, 'code', null, 16],
        "\0".parent::class."\0".'completed_count' => [parent::class, 'completed_count', null, 16],
        "\0".parent::class."\0".'created_at' => [parent::class, 'created_at', null, 16],
        "\0".parent::class."\0".'description' => [parent::class, 'description', null, 16],
        "\0".parent::class."\0".'duration' => [parent::class, 'duration', null, 16],
        "\0".parent::class."\0".'enrolled_count' => [parent::class, 'enrolled_count', null, 16],
        "\0".parent::class."\0".'features' => [parent::class, 'features', null, 16],
        "\0".parent::class."\0".'hasModules' => [parent::class, 'hasModules', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'is_active' => [parent::class, 'is_active', null, 16],
        "\0".parent::class."\0".'learning_outcomes' => [parent::class, 'learning_outcomes', null, 16],
        "\0".parent::class."\0".'level' => [parent::class, 'level', null, 16],
        "\0".parent::class."\0".'mode' => [parent::class, 'mode', null, 16],
        "\0".parent::class."\0".'modules' => [parent::class, 'modules', null, 16],
        "\0".parent::class."\0".'moodleCourseId' => [parent::class, 'moodleCourseId', null, 16],
        "\0".parent::class."\0".'price' => [parent::class, 'price', null, 16],
        "\0".parent::class."\0".'reviews' => [parent::class, 'reviews', null, 16],
        "\0".parent::class."\0".'thumbnail_image' => [parent::class, 'thumbnail_image', null, 16],
        "\0".parent::class."\0".'title' => [parent::class, 'title', null, 16],
        "\0".parent::class."\0".'total_reviews' => [parent::class, 'total_reviews', null, 16],
        "\0".parent::class."\0".'updated_at' => [parent::class, 'updated_at', null, 16],
        "\0".parent::class."\0".'view_count' => [parent::class, 'view_count', null, 16],
        'active_enrollments' => [parent::class, 'active_enrollments', null, 16],
        'average_rating' => [parent::class, 'average_rating', null, 16],
        'banner_image' => [parent::class, 'banner_image', null, 16],
        'category' => [parent::class, 'category', null, 16],
        'certified_count' => [parent::class, 'certified_count', null, 16],
        'code' => [parent::class, 'code', null, 16],
        'completed_count' => [parent::class, 'completed_count', null, 16],
        'created_at' => [parent::class, 'created_at', null, 16],
        'description' => [parent::class, 'description', null, 16],
        'duration' => [parent::class, 'duration', null, 16],
        'enrolled_count' => [parent::class, 'enrolled_count', null, 16],
        'features' => [parent::class, 'features', null, 16],
        'hasModules' => [parent::class, 'hasModules', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'is_active' => [parent::class, 'is_active', null, 16],
        'learning_outcomes' => [parent::class, 'learning_outcomes', null, 16],
        'level' => [parent::class, 'level', null, 16],
        'mode' => [parent::class, 'mode', null, 16],
        'modules' => [parent::class, 'modules', null, 16],
        'moodleCourseId' => [parent::class, 'moodleCourseId', null, 16],
        'price' => [parent::class, 'price', null, 16],
        'reviews' => [parent::class, 'reviews', null, 16],
        'thumbnail_image' => [parent::class, 'thumbnail_image', null, 16],
        'title' => [parent::class, 'title', null, 16],
        'total_reviews' => [parent::class, 'total_reviews', null, 16],
        'updated_at' => [parent::class, 'updated_at', null, 16],
        'view_count' => [parent::class, 'view_count', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}

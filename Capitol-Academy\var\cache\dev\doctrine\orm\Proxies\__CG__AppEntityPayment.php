<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Payment extends \App\Entity\Payment implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'amount' => [parent::class, 'amount', null, 16],
        "\0".parent::class."\0".'course' => [parent::class, 'course', null, 16],
        "\0".parent::class."\0".'createdAt' => [parent::class, 'createdAt', null, 16],
        "\0".parent::class."\0".'currency' => [parent::class, 'currency', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'status' => [parent::class, 'status', null, 16],
        "\0".parent::class."\0".'stripeData' => [parent::class, 'stripeData', null, 16],
        "\0".parent::class."\0".'stripePaymentId' => [parent::class, 'stripePaymentId', null, 16],
        "\0".parent::class."\0".'updatedAt' => [parent::class, 'updatedAt', null, 16],
        "\0".parent::class."\0".'user' => [parent::class, 'user', null, 16],
        'amount' => [parent::class, 'amount', null, 16],
        'course' => [parent::class, 'course', null, 16],
        'createdAt' => [parent::class, 'createdAt', null, 16],
        'currency' => [parent::class, 'currency', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'status' => [parent::class, 'status', null, 16],
        'stripeData' => [parent::class, 'stripeData', null, 16],
        'stripePaymentId' => [parent::class, 'stripePaymentId', null, 16],
        'updatedAt' => [parent::class, 'updatedAt', null, 16],
        'user' => [parent::class, 'user', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}

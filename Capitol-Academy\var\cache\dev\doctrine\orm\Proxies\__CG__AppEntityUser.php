<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class User extends \App\Entity\User implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'country' => [parent::class, 'country', null, 16],
        "\0".parent::class."\0".'createdAt' => [parent::class, 'createdAt', null, 16],
        "\0".parent::class."\0".'dateOfBirth' => [parent::class, 'dateOfBirth', null, 16],
        "\0".parent::class."\0".'email' => [parent::class, 'email', null, 16],
        "\0".parent::class."\0".'firstName' => [parent::class, 'firstName', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'ipAddress' => [parent::class, 'ipAddress', null, 16],
        "\0".parent::class."\0".'isBlocked' => [parent::class, 'isBlocked', null, 16],
        "\0".parent::class."\0".'isVerified' => [parent::class, 'isVerified', null, 16],
        "\0".parent::class."\0".'lastLoginAt' => [parent::class, 'lastLoginAt', null, 16],
        "\0".parent::class."\0".'lastName' => [parent::class, 'lastName', null, 16],
        "\0".parent::class."\0".'password' => [parent::class, 'password', null, 16],
        "\0".parent::class."\0".'phone' => [parent::class, 'phone', null, 16],
        "\0".parent::class."\0".'profilePicture' => [parent::class, 'profilePicture', null, 16],
        "\0".parent::class."\0".'roles' => [parent::class, 'roles', null, 16],
        "\0".parent::class."\0".'updatedAt' => [parent::class, 'updatedAt', null, 16],
        'country' => [parent::class, 'country', null, 16],
        'createdAt' => [parent::class, 'createdAt', null, 16],
        'dateOfBirth' => [parent::class, 'dateOfBirth', null, 16],
        'email' => [parent::class, 'email', null, 16],
        'firstName' => [parent::class, 'firstName', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'ipAddress' => [parent::class, 'ipAddress', null, 16],
        'isBlocked' => [parent::class, 'isBlocked', null, 16],
        'isVerified' => [parent::class, 'isVerified', null, 16],
        'lastLoginAt' => [parent::class, 'lastLoginAt', null, 16],
        'lastName' => [parent::class, 'lastName', null, 16],
        'password' => [parent::class, 'password', null, 16],
        'phone' => [parent::class, 'phone', null, 16],
        'profilePicture' => [parent::class, 'profilePicture', null, 16],
        'roles' => [parent::class, 'roles', null, 16],
        'updatedAt' => [parent::class, 'updatedAt', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}

{#
    Capitol Academy Unified Admin Page Layout Component
    Standardized layout for all admin pages with consistent styling and functionality

    Parameters:
    - page_title: Main page title
    - page_icon: FontAwesome icon for the page
    - create_button: Object with {url, text, icon} for create button
    - search_placeholder: Placeholder text for search input
    - stats: Array of stat objects with {title, value, icon, color, gradient}
    - table_config: Object with table configuration
    - additional_buttons: Array of additional header buttons

    Enhanced with Capitol Academy design standards:
    - Professional card-based layout with shadow effects
    - Brand color scheme (#011a2d navy, #a90418 red, white)
    - Consistent button styling with gradients
    - Responsive design with Bootstrap 5
    - Smooth 300ms transitions
#}

<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Standardized Header with Table Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        {% if page_icon is defined %}
                            <i class="{{ page_icon }} mr-3" style="font-size: 2rem;"></i>
                        {% endif %}
                        {{ page_title|default('Admin Page') }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Professional Search -->
                        <div class="search-container me-3 mb-2 mb-md-0" style="position: relative;">
                            <div class="input-group" style="width: 320px;">
                                <input type="text"
                                       id="professional-search"
                                       class="form-control form-control-lg admin-search-input"
                                       placeholder="{{ search_placeholder|default('Search...') }}"
                                       style="border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;">
                                <div class="input-group-append">
                                    <button type="button"
                                            class="btn btn-lg admin-search-btn"
                                            id="search-clear-btn"
                                            style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="search-results-count" class="text-muted small mt-1" style="display: none;"></div>
                        </div>

                        <!-- Additional Buttons -->
                        {% if additional_buttons is defined %}
                            {% for button in additional_buttons %}
                                <a href="{{ button.url }}"
                                   class="btn {{ button.class|default('btn-outline-light') }} me-2 mb-2 mb-md-0"
                                   style="{{ button.style|default('') }}"
                                   {% if button.title is defined %}title="{{ button.title }}"{% endif %}>
                                    {% if button.icon is defined %}<i class="{{ button.icon }} {% if button.text %}me-2{% endif %}"></i>{% endif %}
                                    {{ button.text }}
                                </a>
                            {% endfor %}
                        {% endif %}

                        <!-- Export CSV Button -->
                        {% if export_button is defined %}
                            <a href="{{ export_button.url }}"
                               class="btn me-2 mb-2 mb-md-0"
                               style="width: 45px; height: 45px; border-radius: 50%; background: white; color: #007bff; border: 2px solid #007bff; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;"
                               onmouseover="this.style.background='#007bff'; this.style.color='white';"
                               onmouseout="this.style.background='white'; this.style.color='#007bff';"
                               title="{{ export_button.title|default('Export CSV') }}">
                                <i class="{{ export_button.icon|default('fas fa-download') }}" style="font-size: 1.1rem;"></i>
                            </a>
                        {% endif %}

                        <!-- Create Button -->
                        {% if create_button is defined %}
                            <a href="{{ create_button.url }}"
                               class="btn btn-light admin-btn-create mb-2 mb-md-0"
                               style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;">
                                {% if create_button.icon is defined %}<i class="{{ create_button.icon }} me-2"></i>{% endif %}
                                {{ create_button.text }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        {% if stats is defined and stats|length > 0 %}
        <div class="card-body pb-0">
            <div class="row mb-4">
                {% for stat in stats %}
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm admin-stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="admin-stat-icon me-3"
                                     style="background: {{ stat.gradient|default('linear-gradient(135deg, #011a2d 0%, #1a3461 100%)') }};">
                                    <i class="{{ stat.icon|default('fas fa-chart-bar') }} text-white"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">{{ stat.title }}</h6>
                                    <h4 class="mb-0" style="color: {{ stat.color|default('#011a2d') }};">{{ stat.value }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Table Content Area -->
        <div class="card-body pt-0">
            {% block table_content %}
                <!-- Table content will be inserted here -->
            {% endblock %}
        </div>
    </div>
</div>

<!-- Professional Confirmation Modals -->
{% block modals %}
    <!-- Status Toggle Confirmation Modal -->
    <div class="modal fade" id="statusToggleModal" tabindex="-1" aria-labelledby="statusToggleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 1rem;">
                    <h6 class="modal-title" id="statusToggleModalLabel" style="font-weight: 600;">
                        <i class="fas fa-toggle-on me-2"></i>Confirm Status Change
                    </h6>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 1rem; text-align: center;">
                    <p class="mb-3" style="color: #011a2d;">
                        Are you sure you want to <span id="statusAction">change</span> "<span id="itemTitle">Item Name</span>"?
                    </p>
                    <small class="text-muted">This action will change the item's status immediately.</small>
                </div>
                <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" style="transition: all 0.3s ease;">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-sm" id="confirmStatusToggle" style="background: #011a2d; color: white; border: none; transition: all 0.3s ease;">
                        <i class="fas fa-check me-1"></i>Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;">
                    <h6 class="modal-title" id="deleteConfirmModalLabel" style="font-weight: 600;">
                        <i class="fas fa-trash me-2"></i>Confirm Deletion
                    </h6>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 1rem; text-align: center;">
                    <p class="mb-3" style="color: #a90418;">
                        Are you sure you want to delete "<span id="deleteItemTitle">Item Name</span>"?
                    </p>
                    <small class="text-muted">This action cannot be undone.</small>
                </div>
                <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" style="transition: all 0.3s ease;">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-sm" id="confirmDelete" style="background: #a90418; color: white; border: none; transition: all 0.3s ease;">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block page_scripts %}
<script>
// Unified Admin Page JavaScript Utilities
class AdminPageUtils {
    static initializeSearch(searchInputId, rowClass, searchFields) {
        const searchInput = document.querySelector(searchInputId);
        const searchBtn = document.querySelector('#search-clear-btn');
        const resultsCount = document.querySelector('#search-results-count');
        const rows = document.querySelectorAll(rowClass);

        let searchTimeout;

        if (!searchInput) return;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                AdminPageUtils.performSearch(this.value.toLowerCase(), rows, searchFields, searchBtn, resultsCount);
            }, 300);
        });

        searchBtn.addEventListener('click', function() {
            if (searchInput.value) {
                searchInput.value = '';
                AdminPageUtils.performSearch('', rows, searchFields, searchBtn, resultsCount);
                searchInput.focus();
            }
        });
    }

    static performSearch(query, rows, searchFields, searchBtn, resultsCount) {
        let visibleCount = 0;

        rows.forEach(row => {
            let isVisible = false;
            
            if (!query) {
                isVisible = true;
            } else {
                searchFields.forEach(field => {
                    const element = row.querySelector(field);
                    if (element && element.textContent.toLowerCase().includes(query)) {
                        isVisible = true;
                    }
                });
            }

            if (isVisible) {
                row.style.display = '';
                row.style.animation = 'fadeIn 0.3s ease-in';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Update search button icon
        if (query) {
            searchBtn.innerHTML = '<i class="fas fa-times"></i>';
        } else {
            searchBtn.innerHTML = '<i class="fas fa-search"></i>';
        }

        // Update results count
        if (resultsCount) {
            if (query) {
                resultsCount.textContent = `${visibleCount} result${visibleCount !== 1 ? 's' : ''} found`;
                resultsCount.style.display = 'block';
            } else {
                resultsCount.style.display = 'none';
            }
        }
    }

    static showStatusModal(itemId, itemTitle, currentStatus, actionCallback) {
        const action = currentStatus ? 'deactivate' : 'activate';
        document.getElementById('statusAction').textContent = action;
        document.getElementById('itemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmStatusToggle');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            actionCallback(itemId, !currentStatus);
        };

        new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
    }

    static showDeleteModal(itemId, itemTitle, deleteCallback) {
        document.getElementById('deleteItemTitle').textContent = itemTitle;

        const confirmBtn = document.getElementById('confirmDelete');
        confirmBtn.onclick = () => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // Show loading state
            AdminPageUtils.showLoadingState();

            deleteCallback(itemId);
        };

        new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
    }

    static showLoadingState() {
        // Create loading overlay if it doesn't exist
        let overlay = document.getElementById('adminLoadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'adminLoadingOverlay';
            overlay.className = 'admin-loading-overlay';
            overlay.innerHTML = `
                <div class="admin-loading-content">
                    <div class="admin-loading-spinner"></div>
                    <p class="admin-loading-text">Processing...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }

    static hideLoadingState() {
        const overlay = document.getElementById('adminLoadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    static showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        document.querySelectorAll('.admin-notification').forEach(n => n.remove());

        const notification = document.createElement('div');
        notification.className = `admin-notification alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 350px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
        `;

        const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle';
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${icon} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, duration);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .admin-stat-card {
        transition: all 0.3s ease;
    }
    
    .admin-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .admin-stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .admin-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(1, 26, 45, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .admin-loading-content {
        text-align: center;
        color: white;
    }

    .admin-loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: adminSpin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    .admin-loading-text {
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0;
    }

    @keyframes adminSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .admin-notification {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Make AdminPageUtils globally available
window.AdminPageUtils = AdminPageUtils;
</script>
{% endblock %}

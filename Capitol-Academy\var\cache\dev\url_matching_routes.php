<?php

/**
 * This file has been auto-generated
 * by the Symfony Routing Component.
 */

return [
    false, // $matchHost
    [ // $staticRoutes
        '/_profiler' => [[['_route' => '_profiler_home', '_controller' => 'web_profiler.controller.profiler::homeAction'], null, null, null, true, false, null]],
        '/_profiler/search' => [[['_route' => '_profiler_search', '_controller' => 'web_profiler.controller.profiler::searchAction'], null, null, null, false, false, null]],
        '/_profiler/search_bar' => [[['_route' => '_profiler_search_bar', '_controller' => 'web_profiler.controller.profiler::searchBarAction'], null, null, null, false, false, null]],
        '/_profiler/phpinfo' => [[['_route' => '_profiler_phpinfo', '_controller' => 'web_profiler.controller.profiler::phpinfoAction'], null, null, null, false, false, null]],
        '/_profiler/xdebug' => [[['_route' => '_profiler_xdebug', '_controller' => 'web_profiler.controller.profiler::xdebugAction'], null, null, null, false, false, null]],
        '/_profiler/open' => [[['_route' => '_profiler_open_file', '_controller' => 'web_profiler.controller.profiler::openAction'], null, null, null, false, false, null]],
        '/admin/categories' => [[['_route' => 'admin_category_index', '_controller' => 'App\\Controller\\Admin\\CategoryController::index'], null, ['GET' => 0], null, false, false, null]],
        '/admin/categories/new' => [[['_route' => 'admin_category_new', '_controller' => 'App\\Controller\\Admin\\CategoryController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/dashboard' => [
            [['_route' => 'admin_dashboard_alt', '_controller' => 'App\\Controller\\Admin\\DashboardController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_dashboard', '_controller' => 'App\\Controller\\AdminController::dashboard'], null, null, null, false, false, null],
        ],
        '/admin/orders' => [
            [['_route' => 'admin_order_index', '_controller' => 'App\\Controller\\Admin\\OrderController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_orders', '_controller' => 'App\\Controller\\AdminController::orders'], null, null, null, false, false, null],
        ],
        '/admin/videos' => [
            [['_route' => 'admin_video_index', '_controller' => 'App\\Controller\\Admin\\VideoController::index'], null, ['GET' => 0], null, false, false, null],
            [['_route' => 'admin_videos', '_controller' => 'App\\Controller\\AdminController::videos'], null, null, null, false, false, null],
        ],
        '/admin/videos/new' => [[['_route' => 'admin_video_new', '_controller' => 'App\\Controller\\Admin\\VideoController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/users' => [[['_route' => 'admin_users', '_controller' => 'App\\Controller\\AdminController::users'], null, null, null, false, false, null]],
        '/admin/courses/export' => [[['_route' => 'admin_courses_export', '_controller' => 'App\\Controller\\AdminController::exportCourses'], null, null, null, false, false, null]],
        '/admin/courses' => [[['_route' => 'admin_courses', '_controller' => 'App\\Controller\\AdminController::courses'], null, null, null, false, false, null]],
        '/admin/courses/create' => [[['_route' => 'admin_course_create', '_controller' => 'App\\Controller\\AdminController::createCourse'], null, null, null, false, false, null]],
        '/admin/contacts' => [[['_route' => 'admin_contacts', '_controller' => 'App\\Controller\\AdminController::contacts'], null, null, null, false, false, null]],
        '/admin/test-email' => [[['_route' => 'admin_test_email', '_controller' => 'App\\Controller\\AdminController::testEmail'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/add-admin' => [[['_route' => 'admin_add_admin', '_controller' => 'App\\Controller\\AdminController::addAdmin'], null, null, null, false, false, null]],
        '/admin/profile' => [[['_route' => 'admin_profile', '_controller' => 'App\\Controller\\AdminController::profile'], null, null, null, false, false, null]],
        '/admin/plans' => [[['_route' => 'admin_plans', '_controller' => 'App\\Controller\\AdminController::plans'], null, null, null, false, false, null]],
        '/admin/plans/create' => [[['_route' => 'admin_plan_create', '_controller' => 'App\\Controller\\AdminController::createPlan'], null, null, null, false, false, null]],
        '/admin/admins' => [[['_route' => 'admin_admins', '_controller' => 'App\\Controller\\AdminController::listAdmins'], null, null, null, false, false, null]],
        '/admin/promotional-banners' => [[['_route' => 'admin_promotional_banners', '_controller' => 'App\\Controller\\AdminController::promotionalBanners'], null, null, null, false, false, null]],
        '/admin/promotional-banners/create' => [[['_route' => 'admin_promotional_banner_create', '_controller' => 'App\\Controller\\AdminController::createPromotionalBanner'], null, null, null, false, false, null]],
        '/admin/partners' => [[['_route' => 'admin_partners', '_controller' => 'App\\Controller\\AdminController::partnersManagement'], null, null, null, false, false, null]],
        '/admin/partners/create' => [[['_route' => 'admin_partners_create', '_controller' => 'App\\Controller\\AdminController::createPartner'], null, null, null, false, false, null]],
        '/admin/enrollments' => [[['_route' => 'admin_enrollments_list', '_controller' => 'App\\Controller\\AdminController::enrollmentsList'], null, null, null, false, false, null]],
        '/admin/enrollments/create' => [[['_route' => 'admin_enrollment_create', '_controller' => 'App\\Controller\\AdminController::createEnrollment'], null, null, null, false, false, null]],
        '/admin/certifications' => [[['_route' => 'admin_certifications', '_controller' => 'App\\Controller\\AdminController::certifications'], null, null, null, false, false, null]],
        '/admin/instructors' => [[['_route' => 'admin_instructor_index', '_controller' => 'App\\Controller\\AdminInstructorController::index'], null, ['GET' => 0], null, true, false, null]],
        '/admin/instructors/new' => [[['_route' => 'admin_instructor_new', '_controller' => 'App\\Controller\\AdminInstructorController::new'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/market_analysis' => [[['_route' => 'admin_market_analysis_index', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::index'], null, ['GET' => 0], null, true, false, null]],
        '/admin/market_analysis/create' => [[['_route' => 'admin_market_analysis_create', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::create'], null, ['GET' => 0, 'POST' => 1], null, false, false, null]],
        '/admin/login' => [[['_route' => 'admin_login', '_controller' => 'App\\Controller\\AdminSecurityController::login'], null, null, null, false, false, null]],
        '/admin/login_check' => [[['_route' => 'admin_login_check', '_controller' => 'App\\Controller\\AdminSecurityController::loginCheck'], null, null, null, false, false, null]],
        '/admin/logout' => [[['_route' => 'admin_logout', '_controller' => 'App\\Controller\\AdminSecurityController::logout'], null, null, null, false, false, null]],
        '/admin/create-admin' => [[['_route' => 'admin_create_admin', '_controller' => 'App\\Controller\\AdminSecurityController::createAdmin'], null, null, null, false, false, null]],
        '/cart' => [[['_route' => 'app_cart', '_controller' => 'App\\Controller\\CartController::index'], null, ['GET' => 0], null, false, false, null]],
        '/cart/add' => [[['_route' => 'app_cart_add', '_controller' => 'App\\Controller\\CartController::add'], null, ['POST' => 0], null, false, false, null]],
        '/cart/remove' => [[['_route' => 'app_cart_remove', '_controller' => 'App\\Controller\\CartController::remove'], null, ['POST' => 0], null, false, false, null]],
        '/cart/update' => [[['_route' => 'app_cart_update', '_controller' => 'App\\Controller\\CartController::update'], null, ['POST' => 0], null, false, false, null]],
        '/cart/clear' => [[['_route' => 'app_cart_clear', '_controller' => 'App\\Controller\\CartController::clear'], null, ['POST' => 0], null, false, false, null]],
        '/cart/count' => [[['_route' => 'app_cart_count', '_controller' => 'App\\Controller\\CartController::count'], null, ['GET' => 0], null, false, false, null]],
        '/cart/summary' => [[['_route' => 'app_cart_summary', '_controller' => 'App\\Controller\\CartController::summary'], null, ['GET' => 0], null, false, false, null]],
        '/cart/widget' => [[['_route' => 'app_cart_widget', '_controller' => 'App\\Controller\\CartController::widget'], null, ['GET' => 0], null, false, false, null]],
        '/cart/validate' => [[['_route' => 'app_cart_validate', '_controller' => 'App\\Controller\\CartController::validate'], null, ['POST' => 0], null, false, false, null]],
        '/checkout' => [[['_route' => 'app_checkout', '_controller' => 'App\\Controller\\CheckoutController::index'], null, ['GET' => 0], null, false, false, null]],
        '/checkout/create-order' => [[['_route' => 'app_checkout_create_order', '_controller' => 'App\\Controller\\CheckoutController::createOrder'], null, ['POST' => 0], null, false, false, null]],
        '/checkout/capture-order' => [[['_route' => 'app_checkout_capture_order', '_controller' => 'App\\Controller\\CheckoutController::captureOrder'], null, ['POST' => 0], null, false, false, null]],
        '/checkout/cancel' => [[['_route' => 'app_checkout_cancel', '_controller' => 'App\\Controller\\CheckoutController::cancel'], null, ['GET' => 0], null, false, false, null]],
        '/checkout/webhook/paypal' => [[['_route' => 'app_checkout_paypal_webhook', '_controller' => 'App\\Controller\\CheckoutController::paypalWebhook'], null, ['POST' => 0], null, false, false, null]],
        '/contact' => [[['_route' => 'app_contact', '_controller' => 'App\\Controller\\ContactController::index'], null, null, null, false, false, null]],
        '/contact/registration' => [[['_route' => 'app_contact_registration', '_controller' => 'App\\Controller\\ContactController::registration'], null, null, null, false, false, null]],
        '/contact/instructor' => [[['_route' => 'app_contact_instructor', '_controller' => 'App\\Controller\\ContactController::instructor'], null, null, null, false, false, null]],
        '/message' => [[['_route' => 'app_message', '_controller' => 'App\\Controller\\ContactController::message'], null, null, null, false, false, null]],
        '/contact/unified' => [[['_route' => 'app_contact_unified', '_controller' => 'App\\Controller\\ContactController::unifiedContact'], null, ['POST' => 0], null, false, false, null]],
        '/courses' => [
            [['_route' => 'app_courses_list', '_controller' => 'App\\Controller\\CourseController::list'], null, null, null, true, false, null],
            [['_route' => 'app_courses', '_controller' => 'App\\Controller\\CourseController::list'], null, null, null, true, false, null],
        ],
        '/connect/google' => [[['_route' => 'connect_google_start', '_controller' => 'App\\Controller\\GoogleAuthController::connectAction'], null, null, null, false, false, null]],
        '/connect/google/check' => [[['_route' => 'connect_google_check', '_controller' => 'App\\Controller\\GoogleAuthController::connectCheckAction'], null, null, null, false, false, null]],
        '/' => [[['_route' => 'app_home', '_controller' => 'App\\Controller\\HomeController::index'], null, null, null, false, false, null]],
        '/about' => [[['_route' => 'app_about', '_controller' => 'App\\Controller\\HomeController::about'], null, null, null, false, false, null]],
        '/partnership' => [[['_route' => 'app_partnership', '_controller' => 'App\\Controller\\HomeController::partnership'], null, null, null, false, false, null]],
        '/diplomas' => [[['_route' => 'app_diplomas', '_controller' => 'App\\Controller\\HomeController::diplomas'], null, null, null, false, false, null]],
        '/instructors' => [[['_route' => 'app_instructors', '_controller' => 'App\\Controller\\HomeController::instructors'], null, null, null, false, false, null]],
        '/executive-program' => [[['_route' => 'app_executive_program', '_controller' => 'App\\Controller\\HomeController::executiveProgram'], null, null, null, false, false, null]],
        '/trading-tools/market-analysis' => [[['_route' => 'app_market_analysis', '_controller' => 'App\\Controller\\MarketAnalysisController::index'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/filter' => [[['_route' => 'api_market_analysis_filter', '_controller' => 'App\\Controller\\MarketAnalysisController::filter'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/featured' => [[['_route' => 'api_market_analysis_featured', '_controller' => 'App\\Controller\\MarketAnalysisController::getFeatured'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/recent' => [[['_route' => 'api_market_analysis_recent', '_controller' => 'App\\Controller\\MarketAnalysisController::getRecent'], null, ['GET' => 0], null, false, false, null]],
        '/api/market-analysis/stats' => [[['_route' => 'api_market_analysis_stats', '_controller' => 'App\\Controller\\MarketAnalysisController::getStats'], null, ['GET' => 0], null, false, false, null]],
        '/forgot-password' => [[['_route' => 'app_forgot_password', '_controller' => 'App\\Controller\\PasswordResetController::forgotPassword'], null, null, null, false, false, null]],
        '/reset-password' => [[['_route' => 'app_reset_password', '_controller' => 'App\\Controller\\PasswordResetController::resetPassword'], null, null, null, false, false, null]],
        '/verify-reset-code' => [[['_route' => 'app_verify_reset_code', '_controller' => 'App\\Controller\\PasswordResetController::verifyResetCode'], null, null, null, false, false, null]],
        '/reset-password-merged' => [[['_route' => 'app_reset_password_merged', '_controller' => 'App\\Controller\\PasswordResetController::resetPasswordMerged'], null, null, null, false, false, null]],
        '/payment/success' => [[['_route' => 'payment_success', '_controller' => 'App\\Controller\\PaymentController::paymentSuccess'], null, null, null, false, false, null]],
        '/payment/cancel' => [[['_route' => 'payment_cancel', '_controller' => 'App\\Controller\\PaymentController::paymentCancel'], null, null, null, false, false, null]],
        '/stripe/webhook' => [[['_route' => 'stripe_webhook', '_controller' => 'App\\Controller\\PaymentController::stripeWebhook'], null, ['POST' => 0], null, false, false, null]],
        '/search' => [[['_route' => 'app_search', '_controller' => 'App\\Controller\\SearchController::search'], null, ['GET' => 0], null, false, false, null]],
        '/api/search/autocomplete' => [[['_route' => 'api_search_autocomplete', '_controller' => 'App\\Controller\\SearchController::autocomplete'], null, ['GET' => 0], null, false, false, null]],
        '/login' => [[['_route' => 'app_login', '_controller' => 'App\\Controller\\SecurityController::login'], null, null, null, false, false, null]],
        '/register' => [[['_route' => 'app_register', '_controller' => 'App\\Controller\\SecurityController::register'], null, null, null, false, false, null]],
        '/logout' => [[['_route' => 'app_logout', '_controller' => 'App\\Controller\\SecurityController::logout'], null, null, null, false, false, null]],
        '/user/home' => [[['_route' => 'app_user_home', '_controller' => 'App\\Controller\\UserController::home'], null, null, null, false, false, null]],
        '/user/profile' => [[['_route' => 'app_user_profile', '_controller' => 'App\\Controller\\UserController::profile'], null, null, null, false, false, null]],
        '/user/orders' => [[['_route' => 'app_user_orders', '_controller' => 'App\\Controller\\UserOrderController::index'], null, ['GET' => 0], null, false, false, null]],
        '/videos' => [
            [['_route' => 'app_videos_list', '_controller' => 'App\\Controller\\VideoController::list'], null, null, null, true, false, null],
            [['_route' => 'app_videos', '_controller' => 'App\\Controller\\VideoController::list'], null, null, null, true, false, null],
        ],
    ],
    [ // $regexpList
        0 => '{^(?'
                .'|/_(?'
                    .'|error/(\\d+)(?:\\.([^/]++))?(*:38)'
                    .'|wdt/([^/]++)(*:57)'
                    .'|profiler/(?'
                        .'|font/([^/\\.]++)\\.woff2(*:98)'
                        .'|([^/]++)(?'
                            .'|/(?'
                                .'|search/results(*:134)'
                                .'|router(*:148)'
                                .'|exception(?'
                                    .'|(*:168)'
                                    .'|\\.css(*:181)'
                                .')'
                            .')'
                            .'|(*:191)'
                        .')'
                    .')'
                .')'
                .'|/admin/(?'
                    .'|c(?'
                        .'|ategories/([^/]++)(?'
                            .'|(*:237)'
                            .'|/toggle\\-(?'
                                .'|status(*:263)'
                                .'|courses(*:278)'
                                .'|videos(*:292)'
                            .')'
                        .')'
                        .'|o(?'
                            .'|urses/(?'
                                .'|([^/]++)/edit(*:328)'
                                .'|(\\d+)/delete(*:348)'
                                .'|(\\d+)/toggle\\-status(*:376)'
                                .'|([^/]++)/preview(*:400)'
                            .')'
                            .'|ntacts/([^/]++)(?'
                                .'|(*:427)'
                                .'|/(?'
                                    .'|preview(*:446)'
                                    .'|toggle\\-processed(*:471)'
                                    .'|delete(*:485)'
                                .')'
                            .')'
                        .')'
                        .'|ertifications/(?'
                            .'|(\\d+)(*:518)'
                            .'|([a-z0-9\\-]+)\\-([A-Z0-9]+)(*:552)'
                        .')'
                    .')'
                    .'|orders/(?'
                        .'|([^/]++)(?'
                            .'|(*:583)'
                            .'|/re(?'
                                .'|fund(*:601)'
                                .'|send\\-access(*:621)'
                            .')'
                        .')'
                        .'|export(*:637)'
                    .')'
                    .'|videos/([^/]++)(?'
                        .'|(*:664)'
                        .'|/(?'
                            .'|edit(*:680)'
                            .'|delete(*:694)'
                            .'|toggle\\-status(*:716)'
                        .')'
                    .')'
                    .'|users/(?'
                        .'|(\\d+)(*:740)'
                        .'|([a-zA-Z0-9\\-_\\.]+)(*:767)'
                        .'|(\\d+)/block(*:786)'
                        .'|(\\d+)/unblock(*:807)'
                        .'|(\\d+)/toggle\\-status(*:835)'
                        .'|(\\d+)/delete(*:855)'
                    .')'
                    .'|admin/(?'
                        .'|([^/]++)/(?'
                            .'|toggle\\-status(*:899)'
                            .'|delete(*:913)'
                        .')'
                        .'|(\\d+)/view(*:932)'
                        .'|(\\d+)/edit(*:950)'
                    .')'
                    .'|p(?'
                        .'|lans/([^/]++)/(?'
                            .'|edit(*:984)'
                            .'|preview(*:999)'
                            .'|toggle\\-status(*:1021)'
                        .')'
                        .'|romotional\\-banners/(?'
                            .'|([^/]++)/edit(*:1067)'
                            .'|(\\d+)/delete(*:1088)'
                            .'|(\\d+)/toggle\\-status(*:1117)'
                        .')'
                        .'|artners/(?'
                            .'|(\\d+)(*:1143)'
                            .'|([a-z0-9\\-]+)(*:1165)'
                            .'|(\\d+)/edit(*:1184)'
                            .'|(\\d+)/delete(*:1205)'
                            .'|(\\d+)/toggle(*:1226)'
                            .'|reorder(*:1242)'
                        .')'
                    .')'
                    .'|enrollments/(?'
                        .'|(\\d+)(*:1273)'
                        .'|([^/\\-]++)\\-([^/]++)(*:1302)'
                        .'|(\\d+)/update\\-progress(*:1333)'
                        .'|(\\d+)/mark\\-completed(*:1363)'
                        .'|(\\d+)/update(*:1384)'
                        .'|(\\d+)/block(*:1404)'
                        .'|(\\d+)/unblock(*:1426)'
                        .'|(\\d+)/delete(*:1447)'
                        .'|(\\d+)/certify(*:1469)'
                    .')'
                    .'|instructors/(?'
                        .'|([^/]++)(?'
                            .'|(*:1505)'
                            .'|/(?'
                                .'|edit(*:1522)'
                                .'|delete(*:1537)'
                                .'|toggle\\-status(*:1560)'
                            .')'
                        .')'
                        .'|reorder(*:1578)'
                        .'|([^/]++)/print(*:1601)'
                    .')'
                    .'|market_analysis/(?'
                        .'|([^/]++)(?'
                            .'|/edit(?'
                                .'|(*:1649)'
                                .'|(*:1658)'
                            .')'
                            .'|(*:1668)'
                        .')'
                        .'|(\\d+)(*:1683)'
                        .'|([^/]++)/(?'
                            .'|delete(*:1710)'
                            .'|toggle\\-status(*:1733)'
                        .')'
                    .')'
                .')'
                .'|/c(?'
                    .'|heckout/success/([^/]++)(*:1774)'
                    .'|ourse(?'
                        .'|s/(?'
                            .'|([^/]++)(?'
                                .'|(*:1807)'
                                .'|/([^/]++)(*:1825)'
                            .')'
                            .'|f(?'
                                .'|inancial\\-markets(*:1856)'
                                .'|undamental\\-analysis(*:1885)'
                            .')'
                            .'|t(?'
                                .'|echnical\\-analysis(*:1917)'
                                .'|rading\\-strategies(*:1944)'
                            .')'
                            .'|p(?'
                                .'|sychological\\-analysis(*:1980)'
                                .'|rofessional\\-trader(*:2008)'
                            .')'
                            .'|capital\\-management(*:2037)'
                            .'|risk\\-management(*:2062)'
                            .'|day\\-trading(*:2083)'
                            .'|enroll/([^/]++)(*:2107)'
                            .'|add\\-to\\-cart/([^/]++)(*:2138)'
                            .'|my\\-courses(*:2158)'
                            .'|by\\-mode/([^/]++)(*:2184)'
                        .')'
                        .'|/([^/]++)/enroll(*:2210)'
                    .')'
                .')'
                .'|/trading\\-tools/market\\-analysis/([^/]++)(*:2262)'
                .'|/user/orders/([^/]++)(*:2292)'
                .'|/videos/(\\d+)(*:2314)'
            .')/?$}sDu',
    ],
    [ // $dynamicRoutes
        38 => [[['_route' => '_preview_error', '_controller' => 'error_controller::preview', '_format' => 'html'], ['code', '_format'], null, null, false, true, null]],
        57 => [[['_route' => '_wdt', '_controller' => 'web_profiler.controller.profiler::toolbarAction'], ['token'], null, null, false, true, null]],
        98 => [[['_route' => '_profiler_font', '_controller' => 'web_profiler.controller.profiler::fontAction'], ['fontName'], null, null, false, false, null]],
        134 => [[['_route' => '_profiler_search_results', '_controller' => 'web_profiler.controller.profiler::searchResultsAction'], ['token'], null, null, false, false, null]],
        148 => [[['_route' => '_profiler_router', '_controller' => 'web_profiler.controller.router::panelAction'], ['token'], null, null, false, false, null]],
        168 => [[['_route' => '_profiler_exception', '_controller' => 'web_profiler.controller.exception_panel::body'], ['token'], null, null, false, false, null]],
        181 => [[['_route' => '_profiler_exception_css', '_controller' => 'web_profiler.controller.exception_panel::stylesheet'], ['token'], null, null, false, false, null]],
        191 => [[['_route' => '_profiler', '_controller' => 'web_profiler.controller.profiler::panelAction'], ['token'], null, null, false, true, null]],
        237 => [[['_route' => 'admin_category_show', '_controller' => 'App\\Controller\\Admin\\CategoryController::show'], ['slug'], ['GET' => 0], null, false, true, null]],
        263 => [[['_route' => 'admin_category_toggle_status', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleStatus'], ['slug'], ['POST' => 0], null, false, false, null]],
        278 => [[['_route' => 'admin_category_toggle_courses', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleCourses'], ['slug'], ['POST' => 0], null, false, false, null]],
        292 => [[['_route' => 'admin_category_toggle_videos', '_controller' => 'App\\Controller\\Admin\\CategoryController::toggleVideos'], ['slug'], ['POST' => 0], null, false, false, null]],
        328 => [[['_route' => 'admin_course_edit', '_controller' => 'App\\Controller\\AdminController::editCourse'], ['code'], null, null, false, false, null]],
        348 => [[['_route' => 'admin_course_delete', '_controller' => 'App\\Controller\\AdminController::deleteCourse'], ['id'], ['POST' => 0], null, false, false, null]],
        376 => [[['_route' => 'admin_course_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleCourseStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        400 => [[['_route' => 'admin_course_preview', '_controller' => 'App\\Controller\\AdminController::previewCourse'], ['code'], null, null, false, false, null]],
        427 => [[['_route' => 'admin_contact_show', '_controller' => 'App\\Controller\\AdminController::contactShow'], ['slug'], null, null, false, true, null]],
        446 => [[['_route' => 'admin_contact_preview', '_controller' => 'App\\Controller\\AdminController::contactPreview'], ['slug'], null, null, false, false, null]],
        471 => [[['_route' => 'admin_contact_toggle_processed', '_controller' => 'App\\Controller\\AdminController::toggleContactProcessed'], ['slug'], ['POST' => 0], null, false, false, null]],
        485 => [[['_route' => 'admin_contact_delete', '_controller' => 'App\\Controller\\AdminController::deleteContact'], ['slug'], ['POST' => 0], null, false, false, null]],
        518 => [[['_route' => 'admin_certification_details', '_controller' => 'App\\Controller\\AdminController::certificationDetails'], ['id'], null, null, false, true, null]],
        552 => [[['_route' => 'admin_certification_details_by_readable', '_controller' => 'App\\Controller\\AdminController::certificationDetailsByReadable'], ['studentName', 'courseCode'], null, null, false, true, null]],
        583 => [[['_route' => 'admin_order_show', '_controller' => 'App\\Controller\\Admin\\OrderController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        601 => [[['_route' => 'admin_order_refund', '_controller' => 'App\\Controller\\Admin\\OrderController::refund'], ['id'], ['POST' => 0], null, false, false, null]],
        621 => [[['_route' => 'admin_order_resend_access', '_controller' => 'App\\Controller\\Admin\\OrderController::resendAccess'], ['id'], ['POST' => 0], null, false, false, null]],
        637 => [[['_route' => 'admin_order_export', '_controller' => 'App\\Controller\\Admin\\OrderController::export'], [], ['GET' => 0], null, false, false, null]],
        664 => [[['_route' => 'admin_video_show', '_controller' => 'App\\Controller\\Admin\\VideoController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        680 => [[['_route' => 'admin_video_edit', '_controller' => 'App\\Controller\\Admin\\VideoController::edit'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        694 => [[['_route' => 'admin_video_delete', '_controller' => 'App\\Controller\\Admin\\VideoController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        716 => [[['_route' => 'admin_video_toggle_status', '_controller' => 'App\\Controller\\Admin\\VideoController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        740 => [[['_route' => 'admin_user_show', '_controller' => 'App\\Controller\\AdminController::userShow'], ['id'], null, null, false, true, null]],
        767 => [[['_route' => 'admin_user_details', '_controller' => 'App\\Controller\\AdminController::userDetails'], ['emailPrefix'], null, null, false, true, null]],
        786 => [[['_route' => 'admin_user_block', '_controller' => 'App\\Controller\\AdminController::blockUser'], ['id'], ['POST' => 0], null, false, false, null]],
        807 => [[['_route' => 'admin_user_unblock', '_controller' => 'App\\Controller\\AdminController::unblockUser'], ['id'], ['POST' => 0], null, false, false, null]],
        835 => [[['_route' => 'admin_user_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleUserStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        855 => [[['_route' => 'admin_user_delete', '_controller' => 'App\\Controller\\AdminController::deleteUser'], ['id'], ['POST' => 0], null, false, false, null]],
        899 => [[['_route' => 'admin_toggle_status', '_controller' => 'App\\Controller\\AdminController::toggleAdminStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        913 => [[['_route' => 'admin_delete', '_controller' => 'App\\Controller\\AdminController::deleteAdmin'], ['id'], ['POST' => 0], null, false, false, null]],
        932 => [[['_route' => 'admin_admin_view', '_controller' => 'App\\Controller\\AdminController::viewAdmin'], ['id'], null, null, false, false, null]],
        950 => [[['_route' => 'admin_admin_edit', '_controller' => 'App\\Controller\\AdminController::editAdmin'], ['id'], null, null, false, false, null]],
        984 => [[['_route' => 'admin_plan_edit', '_controller' => 'App\\Controller\\AdminController::editPlan'], ['code'], null, null, false, false, null]],
        999 => [[['_route' => 'admin_plan_preview', '_controller' => 'App\\Controller\\AdminController::previewPlan'], ['code'], null, null, false, false, null]],
        1021 => [[['_route' => 'admin_plan_toggle_status', '_controller' => 'App\\Controller\\AdminController::togglePlanStatus'], ['code'], ['POST' => 0], null, false, false, null]],
        1067 => [[['_route' => 'admin_promotional_banner_edit', '_controller' => 'App\\Controller\\AdminController::editPromotionalBanner'], ['slug'], null, null, false, false, null]],
        1088 => [[['_route' => 'admin_promotional_banner_delete', '_controller' => 'App\\Controller\\AdminController::deletePromotionalBanner'], ['id'], ['POST' => 0], null, false, false, null]],
        1117 => [[['_route' => 'admin_promotional_banner_toggle_status', '_controller' => 'App\\Controller\\AdminController::togglePromotionalBannerStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1143 => [[['_route' => 'admin_partners_show', '_controller' => 'App\\Controller\\AdminController::showPartner'], ['id'], null, null, false, true, null]],
        1165 => [[['_route' => 'admin_partners_show_by_slug', '_controller' => 'App\\Controller\\AdminController::showPartnerBySlug'], ['slug'], null, null, false, true, null]],
        1184 => [[['_route' => 'admin_partner_edit', '_controller' => 'App\\Controller\\AdminController::editPartner'], ['id'], null, null, false, false, null]],
        1205 => [[['_route' => 'admin_partners_delete', '_controller' => 'App\\Controller\\AdminController::deletePartner'], ['id'], ['POST' => 0], null, false, false, null]],
        1226 => [[['_route' => 'admin_partners_toggle', '_controller' => 'App\\Controller\\AdminController::togglePartner'], ['id'], ['POST' => 0], null, false, false, null]],
        1242 => [[['_route' => 'admin_partners_reorder', '_controller' => 'App\\Controller\\AdminController::reorderPartners'], [], ['POST' => 0], null, false, false, null]],
        1273 => [[['_route' => 'admin_enrollment_details', '_controller' => 'App\\Controller\\AdminController::enrollmentDetails'], ['id'], null, null, false, true, null]],
        1302 => [[['_route' => 'admin_enrollment_details_by_code', '_controller' => 'App\\Controller\\AdminController::enrollmentDetailsByCode'], ['courseCode', 'studentName'], null, null, false, true, null]],
        1333 => [[['_route' => 'admin_enrollment_update_progress', '_controller' => 'App\\Controller\\AdminController::updateEnrollmentProgress'], ['id'], ['POST' => 0], null, false, false, null]],
        1363 => [[['_route' => 'admin_enrollment_mark_completed', '_controller' => 'App\\Controller\\AdminController::markEnrollmentCompleted'], ['id'], ['POST' => 0], null, false, false, null]],
        1384 => [[['_route' => 'admin_enrollment_update', '_controller' => 'App\\Controller\\AdminController::updateEnrollment'], ['id'], ['POST' => 0], null, false, false, null]],
        1404 => [[['_route' => 'admin_enrollment_block', '_controller' => 'App\\Controller\\AdminController::blockEnrollment'], ['id'], ['POST' => 0], null, false, false, null]],
        1426 => [[['_route' => 'admin_enrollment_unblock', '_controller' => 'App\\Controller\\AdminController::unblockEnrollment'], ['id'], ['POST' => 0], null, false, false, null]],
        1447 => [[['_route' => 'admin_enrollment_delete', '_controller' => 'App\\Controller\\AdminController::deleteEnrollment'], ['id'], ['DELETE' => 0], null, false, false, null]],
        1469 => [[['_route' => 'admin_enrollment_certify', '_controller' => 'App\\Controller\\AdminController::certifyEnrollment'], ['id'], ['POST' => 0], null, false, false, null]],
        1505 => [[['_route' => 'admin_instructor_show', '_controller' => 'App\\Controller\\AdminInstructorController::show'], ['emailPrefix'], ['GET' => 0], null, false, true, null]],
        1522 => [[['_route' => 'admin_instructor_edit', '_controller' => 'App\\Controller\\AdminInstructorController::edit'], ['emailPrefix'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1537 => [[['_route' => 'admin_instructor_delete', '_controller' => 'App\\Controller\\AdminInstructorController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        1560 => [[['_route' => 'admin_instructor_toggle_status', '_controller' => 'App\\Controller\\AdminInstructorController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1578 => [[['_route' => 'admin_instructor_reorder', '_controller' => 'App\\Controller\\AdminInstructorController::reorder'], [], ['POST' => 0], null, false, false, null]],
        1601 => [[['_route' => 'admin_instructor_print', '_controller' => 'App\\Controller\\AdminInstructorController::print'], ['id'], ['GET' => 0], null, false, false, null]],
        1649 => [[['_route' => 'admin_market_analysis_edit_readable', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::editBySlug'], ['slug'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1658 => [[['_route' => 'admin_market_analysis_edit', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::edit'], ['id'], ['GET' => 0, 'POST' => 1], null, false, false, null]],
        1668 => [[['_route' => 'admin_market_analysis_show_readable', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::showBySlug'], ['slug'], ['GET' => 0], null, false, true, null]],
        1683 => [[['_route' => 'admin_market_analysis_show', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::show'], ['id'], ['GET' => 0], null, false, true, null]],
        1710 => [[['_route' => 'admin_market_analysis_delete', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::delete'], ['id'], ['POST' => 0], null, false, false, null]],
        1733 => [[['_route' => 'admin_market_analysis_toggle_status', '_controller' => 'App\\Controller\\AdminMarketAnalysisController::toggleStatus'], ['id'], ['POST' => 0], null, false, false, null]],
        1774 => [[['_route' => 'app_checkout_success', '_controller' => 'App\\Controller\\CheckoutController::success'], ['orderNumber'], ['GET' => 0], null, false, true, null]],
        1807 => [[['_route' => 'app_course_show', '_controller' => 'App\\Controller\\CourseController::show'], ['code'], null, null, false, true, null]],
        1825 => [[['_route' => 'app_course_module_show', '_controller' => 'App\\Controller\\CourseController::showModule'], ['courseCode', 'moduleCode'], null, null, false, true, null]],
        1856 => [[['_route' => 'app_course_fma', '_controller' => 'App\\Controller\\CourseController::financialMarkets'], [], null, null, false, false, null]],
        1885 => [[['_route' => 'app_course_fun', '_controller' => 'App\\Controller\\CourseController::fundamentalAnalysis'], [], null, null, false, false, null]],
        1917 => [[['_route' => 'app_course_tec', '_controller' => 'App\\Controller\\CourseController::technicalAnalysis'], [], null, null, false, false, null]],
        1944 => [[['_route' => 'app_course_trs', '_controller' => 'App\\Controller\\CourseController::tradingStrategies'], [], null, null, false, false, null]],
        1980 => [[['_route' => 'app_course_ssa', '_controller' => 'App\\Controller\\CourseController::psychologicalAnalysis'], [], null, null, false, false, null]],
        2008 => [[['_route' => 'app_course_pro', '_controller' => 'App\\Controller\\CourseController::professionalTrader'], [], null, null, false, false, null]],
        2037 => [[['_route' => 'app_course_mma', '_controller' => 'App\\Controller\\CourseController::capitalManagement'], [], null, null, false, false, null]],
        2062 => [[['_route' => 'app_course_rsk', '_controller' => 'App\\Controller\\CourseController::riskManagement'], [], null, null, false, false, null]],
        2083 => [[['_route' => 'app_course_dtr', '_controller' => 'App\\Controller\\CourseController::dayTrading'], [], null, null, false, false, null]],
        2107 => [[['_route' => 'app_course_enroll', '_controller' => 'App\\Controller\\CourseController::enroll'], ['code'], ['POST' => 0], null, false, true, null]],
        2138 => [[['_route' => 'app_course_add_to_cart', '_controller' => 'App\\Controller\\CourseController::addToCart'], ['code'], ['POST' => 0], null, false, true, null]],
        2158 => [[['_route' => 'app_user_courses', '_controller' => 'App\\Controller\\CourseController::myCourses'], [], ['GET' => 0], null, false, false, null]],
        2184 => [[['_route' => 'app_courses_by_mode', '_controller' => 'App\\Controller\\CourseController::byMode'], ['mode'], ['GET' => 0], null, false, true, null]],
        2210 => [[['_route' => 'course_enroll', '_controller' => 'App\\Controller\\PaymentController::enrollInCourse'], ['code'], ['POST' => 0], null, false, false, null]],
        2262 => [[['_route' => 'app_market_analysis_show_seo', '_controller' => 'App\\Controller\\MarketAnalysisController::showBySeoSlug'], ['slug'], ['GET' => 0], null, false, true, null]],
        2292 => [[['_route' => 'app_user_order_show', '_controller' => 'App\\Controller\\UserOrderController::show'], ['orderNumber'], ['GET' => 0], null, false, true, null]],
        2314 => [
            [['_route' => 'app_video_show', '_controller' => 'App\\Controller\\VideoController::show'], ['id'], null, null, false, true, null],
            [null, null, null, null, false, false, 0],
        ],
    ],
    null, // $checkCondition
];

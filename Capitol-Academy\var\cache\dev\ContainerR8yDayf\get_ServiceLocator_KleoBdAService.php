<?php

namespace ContainerR8yDayf;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_KleoBdAService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.kleoBdA' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.kleoBdA'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'banner' => ['privates', '.errored..service_locator.kleoBdA.App\\Entity\\PromotionalBanner', NULL, 'Cannot autowire service ".service_locator.kleoBdA": it needs an instance of "App\\Entity\\PromotionalBanner" but this type has been excluded in "config/services.yaml".'],
        ], [
            'banner' => 'App\\Entity\\PromotionalBanner',
        ]);
    }
}

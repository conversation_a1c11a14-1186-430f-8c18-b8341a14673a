<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/users/show.html.twig */
class __TwigTemplate_a96d62c1302244ec17abd5e3d5fe79dd extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/show.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "User Details: ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 3, $this->source); })()), "fullName", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user mr-3\" style=\"font-size: 2rem;\"></i>
                        User Details: ";
        // line 14
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 14, $this->source); })()), "fullName", [], "any", false, false, false, 14), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Print Button (Icon Only) -->
                        <button onclick=\"printUserDetails()\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid white; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                                onmouseover=\"this.style.background='#f8f9fa'; this.style.transform='scale(1.05)'\"
                                onmouseout=\"this.style.background='white'; this.style.transform='scale(1)'\"
                                title=\"Print User Details\">
                            <i class=\"fas fa-print\" style=\"font-size: 1rem;\"></i>
                        </button>

                        <!-- Back to Users Button -->
                        <a href=\"";
        // line 30
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_users");
        yield "\"
                           class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
            <!-- Profile Picture Section -->
            <div class=\"row mb-4\">
                <div class=\"col-12 text-center\">
                    <div class=\"profile-picture-display\" style=\"display: inline-block;\">
                        ";
        // line 45
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 45, $this->source); })()), "profilePicture", [], "any", false, false, false, 45)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 46
            yield "                            <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("images/uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 46, $this->source); })()), "profilePicture", [], "any", false, false, false, 46))), "html", null, true);
            yield "\" 
                                 alt=\"";
            // line 47
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 47, $this->source); })()), "fullName", [], "any", false, false, false, 47), "html", null, true);
            yield "\" 
                                 class=\"rounded-circle shadow-lg\"
                                 style=\"width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;\">
                        ";
        } else {
            // line 51
            yield "                            <div class=\"rounded-circle shadow-lg d-flex align-items-center justify-content-center\"
                                 style=\"width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;\">
                                ";
            // line 53
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 53, $this->source); })()), "firstName", [], "any", false, false, false, 53))), "html", null, true);
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 53, $this->source); })()), "lastName", [], "any", false, false, false, 53))), "html", null, true);
            yield "
                            </div>
                        ";
        }
        // line 56
        yield "                    </div>
                </div>
            </div>

            <!-- Row 1: Full Name, Date of Birth -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-user text-primary mr-1\"></i>
                            Full Name
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 69
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 69, $this->source); })()), "fullName", [], "any", false, false, false, 69), "html", null, true);
        yield "
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-birthday-cake text-primary mr-1\"></i>
                            Date of Birth
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 80
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 80, $this->source); })()), "dateOfBirth", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 81
            yield "                                ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 81, $this->source); })()), "dateOfBirth", [], "any", false, false, false, 81), "F j, Y"), "html", null, true);
            yield "
                            ";
        } else {
            // line 83
            yield "                                <span class=\"text-muted\">Not provided</span>
                            ";
        }
        // line 85
        yield "                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 2: Email, Phone -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                            Email
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            <a href=\"mailto:";
        // line 99
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 99, $this->source); })()), "email", [], "any", false, false, false, 99), "html", null, true);
        yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 99, $this->source); })()), "email", [], "any", false, false, false, 99), "html", null, true);
        yield "</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                            Phone
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 110
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 110, $this->source); })()), "phone", [], "any", false, false, false, 110)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 111
            yield "                                <a href=\"tel:";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 111, $this->source); })()), "phone", [], "any", false, false, false, 111), "html", null, true);
            yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 111, $this->source); })()), "phone", [], "any", false, false, false, 111), "html", null, true);
            yield "</a>
                            ";
        } else {
            // line 113
            yield "                                <span class=\"text-muted\">Not provided</span>
                            ";
        }
        // line 115
        yield "                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 3: Country, IP Address -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                            Country
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 129
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 129, $this->source); })()), "country", [], "any", false, false, false, 129)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 130
            yield "                                ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 130, $this->source); })()), "country", [], "any", false, false, false, 130), "countryName", [], "any", false, false, false, 130), "html", null, true);
            yield "
                            ";
        } else {
            // line 132
            yield "                                <span class=\"text-muted\">Not specified</span>
                            ";
        }
        // line 134
        yield "                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-network-wired text-primary mr-1\"></i>
                            IP Address
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 144
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["user"] ?? null), "ipAddress", [], "any", true, true, false, 144)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 144, $this->source); })()), "ipAddress", [], "any", false, false, false, 144), "Not recorded")) : ("Not recorded")), "html", null, true);
        yield "
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 4: Registration Date, Status -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                            Registration Date
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 159
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 159, $this->source); })()), "createdAt", [], "any", false, false, false, 159), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-shield-alt text-primary mr-1\"></i>
                            Status
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            ";
        // line 170
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 170, $this->source); })()), "isBlocked", [], "any", false, false, false, 170)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 171
            yield "                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; font-size: 0.9rem;\">
                                    <i class=\"fas fa-ban mr-1\"></i> Blocked
                                </span>
                            ";
        } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,         // line 174
(isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 174, $this->source); })()), "isVerified", [], "any", false, false, false, 174)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 175
            yield "                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 0.9rem;\">
                                    <i class=\"fas fa-check-circle mr-1\"></i> Verified
                                </span>
                            ";
        } else {
            // line 179
            yield "                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; font-size: 0.9rem;\">
                                    <i class=\"fas fa-clock mr-1\"></i> Pending Verification
                                </span>
                            ";
        }
        // line 183
        yield "                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

";
        // line 191
        yield from $this->unwrap()->yieldBlock('stylesheets', $context, $blocks);
        // line 233
        yield "
";
        // line 234
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 191
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 192
        yield "<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #011a2d !important;
    background: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
}

/* Form Label Styling */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 0.5rem;
    color: #1e3c72;
}

/* Print Styles */
@media print {
    .card-header, .btn, .modal { display: none !important; }
    .card-body { border: none !important; border-radius: 0 !important; }
    .enhanced-display-field { border: 1px solid #000 !important; background: white !important; }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 234
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 235
        yield "<script>
// Professional Print Function
function printUserDetails() {
    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Get user data
    const userName = '";
        // line 242
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 242, $this->source); })()), "fullName", [], "any", false, false, false, 242), "html", null, true);
        yield "';
    const userEmail = '";
        // line 243
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 243, $this->source); })()), "email", [], "any", false, false, false, 243), "html", null, true);
        yield "';
    const userPhone = '";
        // line 244
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["user"] ?? null), "phone", [], "any", true, true, false, 244)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 244, $this->source); })()), "phone", [], "any", false, false, false, 244), "Not provided")) : ("Not provided")), "html", null, true);
        yield "';
    const userCountry = '";
        // line 245
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 245, $this->source); })()), "country", [], "any", false, false, false, 245)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 245, $this->source); })()), "country", [], "any", false, false, false, 245), "countryName", [], "any", false, false, false, 245), "html", null, true);
        } else {
            yield "Not specified";
        }
        yield "';
    const userDateOfBirth = '";
        // line 246
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 246, $this->source); })()), "dateOfBirth", [], "any", false, false, false, 246)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 246, $this->source); })()), "dateOfBirth", [], "any", false, false, false, 246), "F j, Y"), "html", null, true);
        } else {
            yield "Not provided";
        }
        yield "';
    const userRegistrationDate = '";
        // line 247
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 247, $this->source); })()), "createdAt", [], "any", false, false, false, 247), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "';
    const userIpAddress = '";
        // line 248
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["user"] ?? null), "ipAddress", [], "any", true, true, false, 248)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 248, $this->source); })()), "ipAddress", [], "any", false, false, false, 248), "Not recorded")) : ("Not recorded")), "html", null, true);
        yield "';
    const userStatus = '";
        // line 249
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 249, $this->source); })()), "isBlocked", [], "any", false, false, false, 249)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "Blocked";
        } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 249, $this->source); })()), "isVerified", [], "any", false, false, false, 249)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "Verified";
        } else {
            yield "Pending Verification";
        }
        yield "';

    // Create the complete HTML document for printing
    const printHTML = `
        <!DOCTYPE html>
        <html lang=\"en\">
        <head>
            <meta charset=\"UTF-8\">
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
            <title>User Details - \${userName} - Capitol Academy</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 12pt;
                    line-height: 1.6;
                    color: #000;
                    background: white;
                    padding: 40px;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 40px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #1e3c72;
                    page-break-inside: avoid;
                }

                .print-logo {
                    font-size: 28pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 8px;
                    letter-spacing: 2px;
                }

                .print-subtitle {
                    font-size: 16pt;
                    color: #2a5298;
                    margin-bottom: 12px;
                    font-style: italic;
                }

                .print-date {
                    font-size: 11pt;
                    color: #666;
                    margin-bottom: 5px;
                }

                .print-section {
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }

                .print-section-title {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 15px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #2a5298;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .print-info-grid {
                    display: table;
                    width: 100%;
                    border-collapse: collapse;
                }

                .print-info-row {
                    display: table-row;
                    border-bottom: 1px solid #eee;
                }

                .print-info-label {
                    display: table-cell;
                    font-weight: bold;
                    color: #333;
                    width: 180px;
                    padding: 12px 15px 12px 0;
                    vertical-align: top;
                }

                .print-info-value {
                    display: table-cell;
                    color: #000;
                    padding: 12px 0;
                    vertical-align: top;
                }
            </style>
        </head>
        <body>
            <!-- Print Header -->
            <div class=\"print-header\">
                <div class=\"print-logo\">CAPITOL ACADEMY</div>
                <div class=\"print-subtitle\">User Details Report</div>
                <div class=\"print-date\">Generated on \${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                })}</div>
            </div>

            <!-- Personal Information Section -->
            <div class=\"print-section\">
                <div class=\"print-section-title\">Personal Information</div>
                <div class=\"print-info-grid\">
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Full Name:</div>
                        <div class=\"print-info-value\">\${userName}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Date of Birth:</div>
                        <div class=\"print-info-value\">\${userDateOfBirth}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Email Address:</div>
                        <div class=\"print-info-value\">\${userEmail}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Phone Number:</div>
                        <div class=\"print-info-value\">\${userPhone}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Country:</div>
                        <div class=\"print-info-value\">\${userCountry}</div>
                    </div>
                </div>
            </div>

            <!-- Account Information Section -->
            <div class=\"print-section\">
                <div class=\"print-section-title\">Account Information</div>
                <div class=\"print-info-grid\">
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Registration Date:</div>
                        <div class=\"print-info-value\">\${userRegistrationDate}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Account Status:</div>
                        <div class=\"print-info-value\">\${userStatus}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">IP Address:</div>
                        <div class=\"print-info-value\">\${userIpAddress}</div>
                    </div>
                </div>
            </div>
        </body>
        </html>
    `;

    // Write the HTML to the print window
    printWindow.document.write(printHTML);
    printWindow.document.close();

    // Wait for content to load, then print
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    };
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/users/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  504 => 249,  500 => 248,  496 => 247,  488 => 246,  480 => 245,  476 => 244,  472 => 243,  468 => 242,  459 => 235,  446 => 234,  395 => 192,  382 => 191,  371 => 234,  368 => 233,  366 => 191,  356 => 183,  350 => 179,  344 => 175,  342 => 174,  337 => 171,  335 => 170,  321 => 159,  303 => 144,  291 => 134,  287 => 132,  281 => 130,  279 => 129,  263 => 115,  259 => 113,  251 => 111,  249 => 110,  233 => 99,  217 => 85,  213 => 83,  207 => 81,  205 => 80,  191 => 69,  176 => 56,  169 => 53,  165 => 51,  158 => 47,  153 => 46,  151 => 45,  133 => 30,  114 => 14,  104 => 6,  91 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}User Details: {{ user.fullName }} - Capitol Academy Admin{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user mr-3\" style=\"font-size: 2rem;\"></i>
                        User Details: {{ user.fullName }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Print Button (Icon Only) -->
                        <button onclick=\"printUserDetails()\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid white; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                                onmouseover=\"this.style.background='#f8f9fa'; this.style.transform='scale(1.05)'\"
                                onmouseout=\"this.style.background='white'; this.style.transform='scale(1)'\"
                                title=\"Print User Details\">
                            <i class=\"fas fa-print\" style=\"font-size: 1rem;\"></i>
                        </button>

                        <!-- Back to Users Button -->
                        <a href=\"{{ path('admin_users') }}\"
                           class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
            <!-- Profile Picture Section -->
            <div class=\"row mb-4\">
                <div class=\"col-12 text-center\">
                    <div class=\"profile-picture-display\" style=\"display: inline-block;\">
                        {% if user.profilePicture %}
                            <img src=\"{{ asset('images/uploads/profiles/' ~ user.profilePicture) }}\" 
                                 alt=\"{{ user.fullName }}\" 
                                 class=\"rounded-circle shadow-lg\"
                                 style=\"width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;\">
                        {% else %}
                            <div class=\"rounded-circle shadow-lg d-flex align-items-center justify-content-center\"
                                 style=\"width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;\">
                                {{ user.firstName|first|upper }}{{ user.lastName|first|upper }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Row 1: Full Name, Date of Birth -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-user text-primary mr-1\"></i>
                            Full Name
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {{ user.fullName }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-birthday-cake text-primary mr-1\"></i>
                            Date of Birth
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {% if user.dateOfBirth %}
                                {{ user.dateOfBirth|date('F j, Y') }}
                            {% else %}
                                <span class=\"text-muted\">Not provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 2: Email, Phone -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                            Email
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            <a href=\"mailto:{{ user.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ user.email }}</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                            Phone
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {% if user.phone %}
                                <a href=\"tel:{{ user.phone }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ user.phone }}</a>
                            {% else %}
                                <span class=\"text-muted\">Not provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 3: Country, IP Address -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                            Country
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {% if user.country %}
                                {{ user.country.countryName }}
                            {% else %}
                                <span class=\"text-muted\">Not specified</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-network-wired text-primary mr-1\"></i>
                            IP Address
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {{ user.ipAddress|default('Not recorded') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 4: Registration Date, Status -->
            <div class=\"row print-two-column clearfix\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                            Registration Date
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {{ user.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-shield-alt text-primary mr-1\"></i>
                            Status
                        </label>
                        <div class=\"form-control-plaintext enhanced-display-field\">
                            {% if user.isBlocked %}
                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; font-size: 0.9rem;\">
                                    <i class=\"fas fa-ban mr-1\"></i> Blocked
                                </span>
                            {% elseif user.isVerified %}
                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 0.9rem;\">
                                    <i class=\"fas fa-check-circle mr-1\"></i> Verified
                                </span>
                            {% else %}
                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; font-size: 0.9rem;\">
                                    <i class=\"fas fa-clock mr-1\"></i> Pending Verification
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #011a2d !important;
    background: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
}

/* Form Label Styling */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 0.5rem;
    color: #1e3c72;
}

/* Print Styles */
@media print {
    .card-header, .btn, .modal { display: none !important; }
    .card-body { border: none !important; border-radius: 0 !important; }
    .enhanced-display-field { border: 1px solid #000 !important; background: white !important; }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Professional Print Function
function printUserDetails() {
    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Get user data
    const userName = '{{ user.fullName }}';
    const userEmail = '{{ user.email }}';
    const userPhone = '{{ user.phone|default(\"Not provided\") }}';
    const userCountry = '{% if user.country %}{{ user.country.countryName }}{% else %}Not specified{% endif %}';
    const userDateOfBirth = '{% if user.dateOfBirth %}{{ user.dateOfBirth|date(\"F j, Y\") }}{% else %}Not provided{% endif %}';
    const userRegistrationDate = '{{ user.createdAt|date(\"F j, Y \\\\a\\\\t g:i A\") }}';
    const userIpAddress = '{{ user.ipAddress|default(\"Not recorded\") }}';
    const userStatus = '{% if user.isBlocked %}Blocked{% elseif user.isVerified %}Verified{% else %}Pending Verification{% endif %}';

    // Create the complete HTML document for printing
    const printHTML = `
        <!DOCTYPE html>
        <html lang=\"en\">
        <head>
            <meta charset=\"UTF-8\">
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
            <title>User Details - \${userName} - Capitol Academy</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 12pt;
                    line-height: 1.6;
                    color: #000;
                    background: white;
                    padding: 40px;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 40px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #1e3c72;
                    page-break-inside: avoid;
                }

                .print-logo {
                    font-size: 28pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 8px;
                    letter-spacing: 2px;
                }

                .print-subtitle {
                    font-size: 16pt;
                    color: #2a5298;
                    margin-bottom: 12px;
                    font-style: italic;
                }

                .print-date {
                    font-size: 11pt;
                    color: #666;
                    margin-bottom: 5px;
                }

                .print-section {
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }

                .print-section-title {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 15px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #2a5298;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .print-info-grid {
                    display: table;
                    width: 100%;
                    border-collapse: collapse;
                }

                .print-info-row {
                    display: table-row;
                    border-bottom: 1px solid #eee;
                }

                .print-info-label {
                    display: table-cell;
                    font-weight: bold;
                    color: #333;
                    width: 180px;
                    padding: 12px 15px 12px 0;
                    vertical-align: top;
                }

                .print-info-value {
                    display: table-cell;
                    color: #000;
                    padding: 12px 0;
                    vertical-align: top;
                }
            </style>
        </head>
        <body>
            <!-- Print Header -->
            <div class=\"print-header\">
                <div class=\"print-logo\">CAPITOL ACADEMY</div>
                <div class=\"print-subtitle\">User Details Report</div>
                <div class=\"print-date\">Generated on \${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                })}</div>
            </div>

            <!-- Personal Information Section -->
            <div class=\"print-section\">
                <div class=\"print-section-title\">Personal Information</div>
                <div class=\"print-info-grid\">
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Full Name:</div>
                        <div class=\"print-info-value\">\${userName}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Date of Birth:</div>
                        <div class=\"print-info-value\">\${userDateOfBirth}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Email Address:</div>
                        <div class=\"print-info-value\">\${userEmail}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Phone Number:</div>
                        <div class=\"print-info-value\">\${userPhone}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Country:</div>
                        <div class=\"print-info-value\">\${userCountry}</div>
                    </div>
                </div>
            </div>

            <!-- Account Information Section -->
            <div class=\"print-section\">
                <div class=\"print-section-title\">Account Information</div>
                <div class=\"print-info-grid\">
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Registration Date:</div>
                        <div class=\"print-info-value\">\${userRegistrationDate}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">Account Status:</div>
                        <div class=\"print-info-value\">\${userStatus}</div>
                    </div>
                    <div class=\"print-info-row\">
                        <div class=\"print-info-label\">IP Address:</div>
                        <div class=\"print-info-value\">\${userIpAddress}</div>
                    </div>
                </div>
            </div>
        </body>
        </html>
    `;

    // Write the HTML to the print window
    printWindow.document.write(printHTML);
    printWindow.document.close();

    // Wait for content to load, then print
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    };
}
</script>
{% endblock %}
{% endblock %}
", "admin/users/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\users\\show.html.twig");
    }
}

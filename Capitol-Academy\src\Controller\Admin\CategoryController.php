<?php

namespace App\Controller\Admin;

use App\Entity\Category;
use App\Repository\CategoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/categories')]
#[IsGranted('ROLE_ADMIN')]
class CategoryController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private CategoryRepository $categoryRepository
    ) {
    }

    #[Route('', name: 'admin_category_index', methods: ['GET'])]
    public function index(Request $request): Response
    {
        $search = $request->query->get('search', '');
        $status = $request->query->get('status', '');

        $queryBuilder = $this->categoryRepository->createQueryBuilder('c');

        if ($search) {
            $queryBuilder
                ->where('c.name LIKE :search')
                ->setParameter('search', '%' . $search . '%');
        }

        if ($status === 'active') {
            $queryBuilder
                ->andWhere('c.isActive = :status')
                ->setParameter('status', true);
        } elseif ($status === 'inactive') {
            $queryBuilder
                ->andWhere('c.isActive = :status')
                ->setParameter('status', false);
        }

        $queryBuilder->orderBy('c.createdAt', 'DESC');

        $categories = $queryBuilder->getQuery()->getResult();

        return $this->render('admin/category/index.html.twig', [
            'categories' => $categories,
            'current_search' => $search,
            'current_status' => $status,
        ]);
    }

    #[Route('/new', name: 'admin_category_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('category_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->redirectToRoute('admin_category_new');
            }

            try {
                $category = new Category();
                $category->setName($request->request->get('name'));
                $category->setDescription($request->request->get('description'));
                $category->setDisplayInCourses((bool) $request->request->get('displayInCourses'));
                $category->setDisplayInVideos((bool) $request->request->get('displayInVideos'));
                $category->setIsActive(true); // Set as active by default

                $this->entityManager->persist($category);
                $this->entityManager->flush();

                $this->addFlash('success', 'Category created successfully.');

                return $this->redirectToRoute('admin_category_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating category: ' . $e->getMessage());
            }
        }

        return $this->render('admin/category/create.html.twig');
    }

    #[Route('/{slug}', name: 'admin_category_show', methods: ['GET'])]
    public function show(string $slug): Response
    {
        $category = $this->categoryRepository->findBySlug($slug);

        if (!$category) {
            throw $this->createNotFoundException('Category not found.');
        }

        return $this->render('admin/category/show.html.twig', [
            'category' => $category,
        ]);
    }



    #[Route('/{slug}/toggle-status', name: 'admin_category_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, string $slug): Response
    {
        $category = $this->categoryRepository->findBySlug($slug);

        if (!$category) {
            throw $this->createNotFoundException('Category not found.');
        }

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            try {
                $category->setIsActive(!$category->isActive());
                $this->entityManager->flush();

                $status = $category->isActive() ? 'activated' : 'deactivated';

                return $this->json([
                    'success' => true,
                    'message' => "Category {$status} successfully.",
                    'newStatus' => $category->isActive()
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the category status.'
                ], 500);
            }
        }

        // Handle regular form submissions
        if ($this->isCsrfTokenValid('toggle'.$category->getId(), $request->request->get('_token'))) {
            $category->setIsActive(!$category->isActive());
            $this->entityManager->flush();

            $status = $category->isActive() ? 'activated' : 'deactivated';
            $this->addFlash('success', "Category {$status} successfully.");
        }

        return $this->redirectToRoute('admin_category_index');
    }

    #[Route('/{slug}/toggle-courses', name: 'admin_category_toggle_courses', methods: ['POST'])]
    public function toggleCourses(Request $request, string $slug): Response
    {
        $category = $this->categoryRepository->findBySlug($slug);

        if (!$category) {
            throw $this->createNotFoundException('Category not found.');
        }

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            try {
                $category->setDisplayInCourses(!$category->isDisplayInCourses());
                $this->entityManager->flush();

                $status = $category->isDisplayInCourses() ? 'activated' : 'deactivated';

                return $this->json([
                    'success' => true,
                    'message' => "Category {$status} in courses successfully.",
                    'newStatus' => $category->isDisplayInCourses()
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the category courses status.'
                ], 500);
            }
        }

        return $this->redirectToRoute('admin_category_index');
    }

    #[Route('/{slug}/toggle-videos', name: 'admin_category_toggle_videos', methods: ['POST'])]
    public function toggleVideos(Request $request, string $slug): Response
    {
        $category = $this->categoryRepository->findBySlug($slug);

        if (!$category) {
            throw $this->createNotFoundException('Category not found.');
        }

        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            try {
                $category->setDisplayInVideos(!$category->isDisplayInVideos());
                $this->entityManager->flush();

                $status = $category->isDisplayInVideos() ? 'activated' : 'deactivated';

                return $this->json([
                    'success' => true,
                    'message' => "Category {$status} in videos successfully.",
                    'newStatus' => $category->isDisplayInVideos()
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the category videos status.'
                ], 500);
            }
        }

        return $this->redirectToRoute('admin_category_index');
    }
}

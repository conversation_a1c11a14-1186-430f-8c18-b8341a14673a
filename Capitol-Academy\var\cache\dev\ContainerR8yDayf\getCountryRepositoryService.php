<?php

namespace ContainerR8yDayf;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCountryRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\CountryRepository' shared autowired service.
     *
     * @return \App\Repository\CountryRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'CountryRepository.php';

        return $container->privates['App\\Repository\\CountryRepository'] = new \App\Repository\CountryRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}

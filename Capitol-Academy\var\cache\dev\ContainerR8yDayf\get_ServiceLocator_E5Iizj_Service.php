<?php

namespace ContainerR8yDayf;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_E5Iizj_Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.e5Iizj.' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.e5Iizj.'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'enrollmentRepository' => ['privates', 'App\\Repository\\EnrollmentRepository', 'getEnrollmentRepositoryService', true],
            'paymentRepository' => ['privates', 'App\\Repository\\PaymentRepository', 'getPaymentRepositoryService', true],
        ], [
            'enrollmentRepository' => 'App\\Repository\\EnrollmentRepository',
            'paymentRepository' => 'App\\Repository\\PaymentRepository',
        ]);
    }
}

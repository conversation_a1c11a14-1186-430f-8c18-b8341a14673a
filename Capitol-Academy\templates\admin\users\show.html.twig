{% extends 'admin/base.html.twig' %}

{% block title %}User Details: {{ user.fullName }} - Capitol Academy Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-user mr-3" style="font-size: 2rem;"></i>
                        User Details: {{ user.fullName }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Print <PERSON><PERSON> (Icon Only) -->
                        <button onclick="printUserDetails()"
                                class="btn me-2 mb-2 mb-md-0"
                                style="width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid white; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;"
                                onmouseover="this.style.background='#f8f9fa'; this.style.transform='scale(1.05)'"
                                onmouseout="this.style.background='white'; this.style.transform='scale(1)'"
                                title="Print User Details">
                            <i class="fas fa-print" style="font-size: 1rem;"></i>
                        </button>

                        <!-- Back to Users Button -->
                        <a href="{{ path('admin_users') }}"
                           class="btn btn-light admin-btn-create mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;">
                            <i class="fas fa-arrow-left me-2"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body p-4" style="background: white; border-radius: 0 0 15px 15px;">
            <!-- Profile Picture Section -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <div class="profile-picture-display" style="display: inline-block;">
                        {% if user.profilePicture %}
                            <img src="{{ asset('images/uploads/profiles/' ~ user.profilePicture) }}" 
                                 alt="{{ user.fullName }}" 
                                 class="rounded-circle shadow-lg"
                                 style="width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;">
                        {% else %}
                            <div class="rounded-circle shadow-lg d-flex align-items-center justify-content-center"
                                 style="width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;">
                                {{ user.firstName|first|upper }}{{ user.lastName|first|upper }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Row 1: Full Name, Date of Birth -->
            <div class="row print-two-column clearfix">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-user text-primary mr-1"></i>
                            Full Name
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {{ user.fullName }}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-birthday-cake text-primary mr-1"></i>
                            Date of Birth
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {% if user.dateOfBirth %}
                                {{ user.dateOfBirth|date('F j, Y') }}
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 2: Email, Phone -->
            <div class="row print-two-column clearfix">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-envelope text-primary mr-1"></i>
                            Email
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            <a href="mailto:{{ user.email }}" class="text-decoration-none" style="color: #011a2d;">{{ user.email }}</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-phone text-primary mr-1"></i>
                            Phone
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {% if user.phone %}
                                <a href="tel:{{ user.phone }}" class="text-decoration-none" style="color: #011a2d;">{{ user.phone }}</a>
                            {% else %}
                                <span class="text-muted">Not provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 3: Country, IP Address -->
            <div class="row print-two-column clearfix">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-globe text-primary mr-1"></i>
                            Country
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {% if user.country %}
                                {{ user.country.countryName }}
                            {% else %}
                                <span class="text-muted">Not specified</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-network-wired text-primary mr-1"></i>
                            IP Address
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {{ user.ipAddress|default('Not recorded') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 4: Registration Date, Status -->
            <div class="row print-two-column clearfix">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-calendar text-primary mr-1"></i>
                            Registration Date
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {{ user.createdAt|date('F j, Y \\a\\t g:i A') }}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-shield-alt text-primary mr-1"></i>
                            Status
                        </label>
                        <div class="form-control-plaintext enhanced-display-field">
                            {% if user.isBlocked %}
                                <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; font-size: 0.9rem;">
                                    <i class="fas fa-ban mr-1"></i> Blocked
                                </span>
                            {% elseif user.isVerified %}
                                <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 0.9rem;">
                                    <i class="fas fa-check-circle mr-1"></i> Verified
                                </span>
                            {% else %}
                                <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; font-size: 0.9rem;">
                                    <i class="fas fa-clock mr-1"></i> Pending Verification
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #011a2d !important;
    background: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
}

/* Form Label Styling */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 0.5rem;
    color: #1e3c72;
}

/* Print Styles */
@media print {
    .card-header, .btn, .modal { display: none !important; }
    .card-body { border: none !important; border-radius: 0 !important; }
    .enhanced-display-field { border: 1px solid #000 !important; background: white !important; }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Professional Print Function
function printUserDetails() {
    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Get user data
    const userName = '{{ user.fullName }}';
    const userEmail = '{{ user.email }}';
    const userPhone = '{{ user.phone|default("Not provided") }}';
    const userCountry = '{% if user.country %}{{ user.country.countryName }}{% else %}Not specified{% endif %}';
    const userDateOfBirth = '{% if user.dateOfBirth %}{{ user.dateOfBirth|date("F j, Y") }}{% else %}Not provided{% endif %}';
    const userRegistrationDate = '{{ user.createdAt|date("F j, Y \\a\\t g:i A") }}';
    const userIpAddress = '{{ user.ipAddress|default("Not recorded") }}';
    const userStatus = '{% if user.isBlocked %}Blocked{% elseif user.isVerified %}Verified{% else %}Pending Verification{% endif %}';

    // Create the complete HTML document for printing
    const printHTML = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>User Details - ${userName} - Capitol Academy</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                body {
                    font-family: 'Times New Roman', serif;
                    font-size: 12pt;
                    line-height: 1.6;
                    color: #000;
                    background: white;
                    padding: 40px;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 40px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #1e3c72;
                    page-break-inside: avoid;
                }

                .print-logo {
                    font-size: 28pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 8px;
                    letter-spacing: 2px;
                }

                .print-subtitle {
                    font-size: 16pt;
                    color: #2a5298;
                    margin-bottom: 12px;
                    font-style: italic;
                }

                .print-date {
                    font-size: 11pt;
                    color: #666;
                    margin-bottom: 5px;
                }

                .print-section {
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }

                .print-section-title {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #1e3c72;
                    margin-bottom: 15px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #2a5298;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .print-info-grid {
                    display: table;
                    width: 100%;
                    border-collapse: collapse;
                }

                .print-info-row {
                    display: table-row;
                    border-bottom: 1px solid #eee;
                }

                .print-info-label {
                    display: table-cell;
                    font-weight: bold;
                    color: #333;
                    width: 180px;
                    padding: 12px 15px 12px 0;
                    vertical-align: top;
                }

                .print-info-value {
                    display: table-cell;
                    color: #000;
                    padding: 12px 0;
                    vertical-align: top;
                }
            </style>
        </head>
        <body>
            <!-- Print Header -->
            <div class="print-header">
                <div class="print-logo">CAPITOL ACADEMY</div>
                <div class="print-subtitle">User Details Report</div>
                <div class="print-date">Generated on ${new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                })}</div>
            </div>

            <!-- Personal Information Section -->
            <div class="print-section">
                <div class="print-section-title">Personal Information</div>
                <div class="print-info-grid">
                    <div class="print-info-row">
                        <div class="print-info-label">Full Name:</div>
                        <div class="print-info-value">${userName}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">Date of Birth:</div>
                        <div class="print-info-value">${userDateOfBirth}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">Email Address:</div>
                        <div class="print-info-value">${userEmail}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">Phone Number:</div>
                        <div class="print-info-value">${userPhone}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">Country:</div>
                        <div class="print-info-value">${userCountry}</div>
                    </div>
                </div>
            </div>

            <!-- Account Information Section -->
            <div class="print-section">
                <div class="print-section-title">Account Information</div>
                <div class="print-info-grid">
                    <div class="print-info-row">
                        <div class="print-info-label">Registration Date:</div>
                        <div class="print-info-value">${userRegistrationDate}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">Account Status:</div>
                        <div class="print-info-value">${userStatus}</div>
                    </div>
                    <div class="print-info-row">
                        <div class="print-info-label">IP Address:</div>
                        <div class="print-info-value">${userIpAddress}</div>
                    </div>
                </div>
            </div>
        </body>
        </html>
    `;

    // Write the HTML to the print window
    printWindow.document.write(printHTML);
    printWindow.document.close();

    // Wait for content to load, then print
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    };
}
</script>
{% endblock %}
{% endblock %}

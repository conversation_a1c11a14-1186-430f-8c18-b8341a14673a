<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\Container8BoVpN5\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/Container8BoVpN5/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/Container8BoVpN5.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\Container8BoVpN5\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \Container8BoVpN5\App_KernelDevDebugContainer([
    'container.build_hash' => '8BoVpN5',
    'container.build_id' => 'ae9d4dcd',
    'container.build_time' => 1752493661,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'Container8BoVpN5');
